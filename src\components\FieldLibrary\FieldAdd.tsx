import { Modal, Input, Form } from 'antd'
import FormItem from 'antd/es/form/FormItem'
import TextArea from 'antd/lib/input/TextArea'
import React, { useEffect } from 'react'
import {
  QueryObserverResult,
  RefetchOptions,
  RefetchQueryFilters,
  useMutation,
} from 'react-query'
import { fieldSave } from '../../api/fieldLibrary'
import { extractResponse } from '../../api/util'
import { PageData } from '../../api/types'

type FieldAddType = {
  open: boolean
  close: React.Dispatch<React.SetStateAction<boolean>>
  activeLibrary: RuleLibrary
  refetch: <TPageData>(
    options?: (RefetchOptions & RefetchQueryFilters<TPageData>) | undefined
  ) => Promise<QueryObserverResult<PageData<FieldType> | undefined, unknown>>
}

export default function FieldAdd({
  open,
  close,
  activeLibrary,
  refetch,
}: FieldAddType) {
  const form = Form.useForm()[0]

  useEffect(() => {
    if (open) {
      form.setFieldsValue({ fieldName: '', fieldDescribe: '' })
    }
  })

  const fieldSaveMutation = useMutation(extractResponse(fieldSave), {
    onSuccess(res) {
      console.log(res)
      close(false)
      refetch()
    },
  })

  function submit(values: any) {
    fieldSaveMutation.mutate({ ...values, libraryId: activeLibrary.id })
  }

  return (
    <Modal
      title="新建字段"
      open={open}
      onCancel={() => {
        close(false)
      }}
      onOk={() => {
        form.submit()
      }}
    >
      <Form
        form={form}
        onFinish={submit}
        labelCol={{ span: 7 }}
        style={{ width: '380px' }}
        className="!ml-6 !mt-6"
      >
        <FormItem
          label="字段名称"
          rules={[{ required: true, message: '请输入字段库表名称' }]}
          name="fieldName"
        >
          <Input
            maxLength={40}
            onChange={e => {
              form.setFieldValue('fieldName', e.target.value.trim())
            }}
          />
        </FormItem>
        <FormItem label="字段描述" name="fieldDescribe">
          <TextArea maxLength={40} />
        </FormItem>
      </Form>
    </Modal>
  )
}
