import { But<PERSON>, Checkbox, Input<PERSON><PERSON>ber, Tooltip,Popover} from 'antd'

import React, { useEffect, useState } from 'react'
import { useHotkeys } from 'react-hotkeys-hook'
import {
  CATALOG_ZOOM,
  CATALOG_ZOOM_KEY,
  CATALOG_CHECK_KEY,
} from '../../constants'
import { getLocalStorage, setLocalStorage } from '../../utils'
import { useDiyHotkeyWithTypes } from '../../hooks/useDiyHotkey'
import useCutImage from './useCutImage'

type Props = {
  isOpen?: boolean
  image?: HTMLImageElement
  activeImageIndex?: number
  setActiveImageIndex: React.Dispatch<React.SetStateAction<number>>
  imageList: ImageType[]
  switchPiece: (type: 'left' | 'right') => void
  allocationData?: UserCheckType
  togglePage: (type: 'left' | 'right') => void
}



export default function ImageCanvas({
  isOpen,
  image,
  activeImageIndex,
  setActiveImageIndex,
  imageList,
  switchPiece,
  allocationData,
  togglePage,
}: Props) {
  const error_dict = {"0":"档案实体","1":"数字化副本","2":"筛密","3":"OCR","4":"条目筛选"}
  const [rotate, setRotate] = useState(0)
  const [defaultCheck, setDefaultCheck] = useState<string>('1')
  const [defaultZoom, setDefaultZoom] = React.useState<number>(CATALOG_ZOOM)
  const canvasRef = React.useRef<HTMLCanvasElement>(null)
  const containerRef = React.useRef<HTMLDivElement>(null)
  const prevBtn = React.useRef<HTMLButtonElement>(null)
  const {
    MAN_CHECK_PREV,
    MAN_CHECK_NEXT,
    MAN_CHECK_NEXT_VOLUME,
    MAN_CHECK_PREV_VOLUME,
  } = useDiyHotkeyWithTypes()

  useHotkeys(MAN_CHECK_PREV.key, () => togglePage('left'), {
    enabled: MAN_CHECK_PREV.filter,
  })
  useHotkeys(MAN_CHECK_NEXT.key, () => togglePage('right'), {
    // enabled: MAN_CHECK_NEXT.filter,
  })
  useHotkeys(MAN_CHECK_NEXT_VOLUME.key, () => switchPiece('right'), {
    // enabled: MAN_CHECK_NEXT_VOLUME.filter,
  })
  useHotkeys(MAN_CHECK_PREV_VOLUME.key, () => switchPiece('left'), {
    // enabled: MAN_CHECK_PREV_VOLUME.filter,
  })

  const { zoom, setZoom } = useCutImage({
    isOpen,
    rotate,
    canvasRef,
    containerRef,
    activeImageIndex,
    imageList,
  })

  useEffect(() => {
    setRotate(0)
  }, [activeImageIndex])

  React.useEffect(() => {
    if (isOpen) {
      const localValue = String(getLocalStorage(CATALOG_CHECK_KEY))
      const localZoom = String(getLocalStorage(`CATALOG_ZOOM`))
      setDefaultZoom(Number(localZoom))
      setDefaultCheck(localValue)
    }
  }, [isOpen])

  //保存默认缩放
  function handleSave() {
    if (defaultCheck) {
      setLocalStorage(CATALOG_ZOOM_KEY, String(defaultZoom))
    }
  }

  function onChangeDefaultZoom(value: number | null) {
    let newValue = !value ? 1 : Math.floor(Number(value))
    if (newValue > 300) {
      newValue = 300
    }
    setDefaultZoom(newValue)
  }

  //默认输入框的onBlur事件
  function onBlurDefaultZoom() {
    console.log(defaultZoom)
    handleSave()
  }

  function onChangeZoom(value: number | null) {
    if (!value) {
      return
    }
    let newValue = !value ? 1 : Math.floor(Number(value))
    if (newValue > 300) {
      newValue = 300
    }

    setZoom(newValue)
  }

  // function getErrorMsg(value:string){
  //     let msg = []
  //     const split_msg = value.split(",")
  //     for (let i=0;i<split_msg.length;i++){
  //       msg.push(split_msg[i] in error_dict ? error_dict[split_msg[i] as keyof typeof error_dict] : "undefined")
  //   }
  //     return msg.toString()
  // }

  return (
    <div className="relative flex flex-col flex-1 flex-shrink-0 bg-[#F7F7F7] overflow-hidden">
      <div className="flex items-center  pt-[10px]">
        {/* <Button
          type="text"
          className="text-[#353C4C] ml-4"
          icon={
            <span className="anticon anticon-step-backward">
              <ReloadIcon className="w-4 h-4 fill-current" />
            </span>
          }
          onClick={() => setRotate(prev => (prev + 1) % 4)}
        >
          旋转图像
        </Button> */}

        <Checkbox
          id="toggle-default-scale"
          className="mr-2 ml-6"
          checked={defaultCheck === '1'}
          onChange={e => {
            setDefaultCheck(e.target.checked ? '1' : '0')
            // handleSave()
            //这里写在handleSave中总是获取不到最新的defaultCheck,所以暂时写在下面
            setLocalStorage(CATALOG_CHECK_KEY, e.target.checked ? '1' : '0')
          }}
        >
          默认缩放比
        </Checkbox>
        <InputNumber
          className="w-[49px] h-6"
          value={defaultZoom}
          type="number"
          controls={false}
          size="small"
          step={1}
          min={1}
          max={300}
          onChange={e => onChangeDefaultZoom(e)}
          onBlur={e => onBlurDefaultZoom()}
        />
        <span className="text-sm ml-1">%</span>

        <label className="text-sm ml-6 mr-2" htmlFor="toggle-default-scale">
          当前缩放比
        </label>
        <InputNumber
          className="w-[49px] h-6 ml-2"
          value={zoom}
          controls={false}
          size="small"
          step={1}
          min={1}
          max={300}
          onChange={e => onChangeZoom(e)}
        />
        <span className="text-sm ml-1">%</span>
        {allocationData?.errorType ? (
          <div className="flex items-center text-[#F5222D] ml-6  flex-1">
            <div className=" rounded-full w-[6px] h-[6px] bg-[#F5222D]" />
            <Popover content={'错误类型：'+allocationData.errorType+"不合格"}>
              <div className="ml-2 text-sm !w-[214px] overflow-hidden text-ellipsis whitespace-nowrap">
                错误类型：{allocationData.errorType}不合格
              </div>
            </Popover>


            <Popover content={'错误原因：'+allocationData.remarks}>
              <div className="ml-2 text-sm !w-[214px] overflow-hidden text-ellipsis whitespace-nowrap">
                错误原因：{allocationData.remarks}
              </div>
            </Popover>
          </div>
        ) : null}
      </div>
      <div className="relative flex  flex-1 flex-shrink-0 bg-gray-light  overflow-auto">
        <div className="relative flex-1 flex " ref={containerRef}>
          <div
            style={{
              width: canvasRef.current?.style.width,
              height: canvasRef.current?.style.height,
            }}
            className="absolute  mx-auto top-0 left-0 right-0 bottom-0"
          >
            {/*当前图像展示区域*/}
            <canvas className="m-auto" ref={canvasRef} />
          </div>
        </div>
      </div>
      <div className="h-12 bg-white flex items-center">
        <Tooltip
          title="shift  ←"
          color="white"
          overlayInnerStyle={{ color: `black` }}
        >
          <Button
            onClick={() => {
              switchPiece('left')
            }}
          >
            上一件
          </Button>
        </Tooltip>
        <Tooltip title="←" color="white" overlayInnerStyle={{ color: `black` }}>
          <Button
            className="ml-auto"
            onClick={() => togglePage('left')}
            ref={prevBtn}
          >
            上一页
          </Button>
        </Tooltip>
        <Tooltip title="→" color="white" overlayInnerStyle={{ color: `black` }}>
          <Button className="ml-4" onClick={() => togglePage('right')}>
            下一页
          </Button>
        </Tooltip>
        <Tooltip
          title="shift →"
          color="white"
          overlayInnerStyle={{ color: `black` }}
        >
          <Button
            className="ml-auto"
            onClick={() => {
              switchPiece('right')
            }}
          >
            下一件
          </Button>
        </Tooltip>
      </div>
    </div>
  )
}
