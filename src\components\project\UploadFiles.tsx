import { Input, Modal, Upload, UploadProps,Spin} from 'antd'
import React, { useEffect, useState } from 'react'
import FormItem from 'antd/es/form/FormItem'
import { LeftOutlined } from '@ant-design/icons'
import {
  QueryObserverResult,
  RefetchOptions,
  RefetchQueryFilters,
  UseMutationResult,
  useMutation,
} from 'react-query'
import { AxiosResponse } from 'axios'
import cloud from '../../style/img/cloud.png'
import server from '../../style/img/server.png'
import { serverUpload } from '../../api/project'
import { PageData, Result } from '../../api/types'
import useUploadFiles from '../../hooks/useUploadFiles'

type UploadFilesType = {
  open: boolean
  close: React.Dispatch<React.SetStateAction<boolean>>
  type: string
  activeConfig?: CatalogueFile
  refetch: <TPageData>(
    options?: (RefetchOptions & RefetchQueryFilters<TPageData>) | undefined
  ) => Promise<QueryObserverResult<CatalogueFile[], unknown>>
  projectConfigSaveMutation: UseMutationResult<
    AxiosResponse<Result<PageData<ProjectType>>, any>,
    unknown,
    CatalogueFile,
    unknown
  >
}

export default function UploadFiles({
  open,
  close,
  type,
  activeConfig,
  refetch,
  projectConfigSaveMutation,
}: UploadFilesType) {
  const [activeUploadType, setActiveUploadType] = useState('')
  const [text, setText] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [progressShow, setProgressShow] = useState(false)
  console.log({ type })
  useEffect(() => {
    if (open) {
      setActiveUploadType('')
      setText('')
      setProgressShow(false)
    }
  }, [open])

  const {
    doUpload,
    uploadFileMap,
    allKeyList,
    waitingWithUploadKeyList,
    setErrorBlog,
  } = useUploadFiles({
    activeConfig: activeConfig!,
    visible: open,
    type: type,
  })

  const props: UploadProps = {
    name: 'file',
    action: `/api/exactness/taskUpload/${type}?taskConfigId=${activeConfig?.id}`,
    data: (file: Record<string, any>) => {
      return { fileName: file.webkitRelativePath }
    },
    headers: {
      authorization: 'authorization-text',
    },

    directory: type === 'image',
    itemRender() {},
    onChange(info) {
      if (info.file.status === 'done') {
        console.log(info)

        refetch().then(p => {
          if (
            activeConfig?.ruleConfigType === 0 ||
            activeConfig?.ruleConfigType === 1
          ) {
            const config = p.data?.find(item => item.id === activeConfig?.id)
            if (config) {
              projectConfigSaveMutation.mutate({
                ...config,
                sheetName: '',
                // sheetNames: '',
                ruleMapping: '',
              })
            }
          }
        })
        close(false)
      }
    },
  }

  function onChangeInput(e: React.ChangeEvent<HTMLInputElement>) {
    const { files } = e.target

    const keyList: string[] = []
    const fileMap: Record<string, File> = {}
    if (files?.length) {
      Object.values(files).forEach((file, index) => {
        const key = String(index)
        keyList.push(String(index))
        fileMap[key] = file
      })
      doUpload(keyList, fileMap)
      setProgressShow(true)
      e.target.value = ''
    }
  }

  const serverUploadMutation = useMutation(serverUpload, {
    onSuccess(res) {
      console.log(res)
      setIsLoading(false)
      refetch()
      close(false)
    },
  })

  return (
    <Modal
      title="文件上传"
      open={open}
      onCancel={() => {
        close(false)
        refetch()
      }}
      onOk={() => {
        if (activeUploadType === 'server') {
          setIsLoading(true)
          const obj: {
            url: string
            filePath: string
            taskConfigId: string
            rule?: string
          } = {
            url: `/local/${type}`,
            filePath: text,
            taskConfigId: activeConfig?.id!,
          }
          if (type === 'image') {
            obj.rule = activeConfig?.urlRule
          }

          serverUploadMutation.mutate(obj)
        } else {
          close(false)
          refetch()
        }
      }}
    >
      {activeUploadType === '' ? (
        <>
          {activeConfig?.ruleConfigType !== 0 &&
          activeConfig?.ruleConfigType !== 1 ? (
            <div
              className=" w-[408px] h-[94px] bg-black bg-opacity-[2%] cursor-pointer mx-auto pt-1 items-center justify-center"
              onClick={() => {
                setActiveUploadType('server')
              }}
            >
              <div className=" w-[58px] h-[58px] mx-auto">
                <img src={server} alt="服务器上传" />
              </div>
              <div className="mx-auto w-20 text-base text-center">
                服务器上传
              </div>
            </div>
          ) : null}
          {type !== 'image' && type !== 'pdf' && type !== 'ofd' ? (
            <Upload
              {...props}
              className="block w-[408px] h-[94px] bg-black bg-opacity-[2%] cursor-pointer !mt-4 !mx-auto pt-1"
            >
              <div className="w-[408px] h-[94px]  ">
                <div className="w-[58px] h-[58px] mx-auto">
                  <img src={cloud} alt="本地上传" />
                </div>

                <div className="mx-auto w-20 text-base text-center">
                  本地上传
                </div>
              </div>
            </Upload>
          ) : (
            <label
              className="block w-[408px] h-[94px] bg-black bg-opacity-[2%] cursor-pointer !mt-4 !mx-auto pt-1"
              // htmlFor={inputIdRef.current}
            >
              <input
                className="opacity-0 absolute block left-0 top-0 w-[408px] h-[94px] cursor-pointer"
                type="file"
                // @ts-ignore
                webkitdirectory="true"
                directory="true"
                onChange={onChangeInput}
              />
              <div className="w-[408px] h-[94px]  ">
                <div className="w-[58px] h-[58px] !mx-auto">
                  <img src={cloud} alt="本地上传" />
                </div>

                <div className="mx-auto w-20 text-base text-center">
                  本地上传
                </div>
              </div>
            </label>
          )}
        </>
      ) : (
        <div>
          <div
            className=" cursor-pointer"
            onClick={() => {
              setActiveUploadType('')
            }}
          >
            <LeftOutlined className="mr-1" />
            返回
          </div>
          <div className="mt-4">
            <FormItem label="服务器地址" className="ml-8">
              <Input
                className="h-8 w-64 ml-2"
                value={text}
                onChange={e => {
                  setText(e.target.value)
                }}
              />
            </FormItem>
          </div>
          {
            isLoading?(
              <div className="mt-12 h-12">
                <Spin tip="Loading" size="large">
                  <div className="content" />
                </Spin>
            </div>):""
          }
        </div>
      )}
      {progressShow ? (
        <div className="mt-4">
          图片上传进度
          <span className="ml-2">
            {allKeyList.length -
              waitingWithUploadKeyList.length +
              '/' +
              allKeyList.length}
          </span>
          <div className="mt-1 w-[472px] h-1 bg-gray-300  relative">
            <div
              style={{
                width:
                  ((allKeyList.length - waitingWithUploadKeyList.length) /
                    allKeyList.length) *
                    472 +
                  'px',
              }}
              className="absolute top-0 h-1 left-0  bg-primary-default"
            />
          </div>
        </div>
      ) : null}
    </Modal>
  )
}
