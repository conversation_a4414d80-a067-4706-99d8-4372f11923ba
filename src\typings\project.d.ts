// 任务
declare interface ProjectType {
  id?: string
  taskName: string
  taskStatus: TProcessStatus //进度
  taskProgress: number //具体进度数字
  userCheckProgress: number //人工进度
  manDoneNumber: number //人工已经完成进度
  total: number //总数量
  createUserName: string //创建人名字
  createUserId: number // 创建人id
  createTime: string //创建时间
  libraryId: string
  manualRatio: string //人工审查
  userCheckCount: number
  isShow?: number
}

type TProcessStatus = 0 | 1 | 2 | 3

declare interface ProjectBindDataDO {
  taskId?: string
  taskName?: string
  libraryId?: string
  exactnessScale?: string
}

declare interface CatalogueFile {
  id?: string
  sheetName: string // sheet表
  ruleTemplateId?: string // 规则模板
  filePath: string //档案输入路径
  urlRule: string //图像规则
  ruleConfigType: number
  taskId?: string
  libraryId: string
  sheetNames?: string
  ruleKeyFields?: string
  ruleMapping?: string
  pieceHaveIs: number
  fileName: string
}

// 统计数据
declare type ImageStatisticType = {
  ruleName?: string
  ruleType?: string
  errorType?: number
  errorNum?: number
  errorRate?: number
  fileName?: number
  lists?: ImageRuleInfoType[]
  taskStatisticsFieldVOs?: taskStatisticsFieldType[]
}

declare type taskStatisticsFieldType = {
  errorNum: number
  errorRate: number
  fieldName: string
}

declare type ImageRuleInfoType = {
  errorFileName: string
  id: string
  createTime: string
  dataKey: string
  fieldName: string
  errorCoordinate: string
  ruleName: string
}

declare type PieceType = {
  piece: string
  fieldData?: string
}

declare type FieldInspectType = {
  taskJson: string
  partNumber?: string
}

