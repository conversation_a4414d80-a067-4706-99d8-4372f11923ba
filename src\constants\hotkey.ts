export enum HotkeyType {
  'MAN_CHECK_PREV' = 'MAN_CHECK_PREV',
  'MAN_CHECK_NEXT' = 'MAN_CHECK_NEXT',
  'MAN_CHECK_NEXT_VOLUME' = 'MAN_CHECK_NEXT_VOLUME',
  'MAN_CHECK_PREV_VOLUME' = 'MAN_CHECK_PREV_VOLUME',
  'MAN_CHECK_PASS' = 'MAN_CHECK_PASS',
}

export const DEFAULT_HOTKEY_MAP = {
  [HotkeyType.MAN_CHECK_PREV]: {
    keyList: ['left'],
    preventDefault: false,
  },
  [HotkeyType.MAN_CHECK_NEXT]: {
    keyList: ['right'],
    preventDefault: false,
  },
  [HotkeyType.MAN_CHECK_PREV_VOLUME]: {
    keyList: ['shift', 'left'],
    preventDefault: false,
  },
  [HotkeyType.MAN_CHECK_NEXT_VOLUME]: {
    keyList: ['shift', 'right'],
    preventDefault: false,
  },
  [HotkeyType.MAN_CHECK_PASS]: {
    keyList: ['enter'],
    preventDefault: false,
  },
}

export const FIXED_HOTKEY: HotkeyType[] = [
  // HotkeyType.COMMON_PREV,
  // HotkeyType.COMMON_NEXT,
  // HotkeyType.CATALOG_AI_PREV,
  // HotkeyType.CATALOG_AI_NEXT,
]
