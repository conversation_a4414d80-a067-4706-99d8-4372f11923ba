import { Input, Form, Checkbox, Button, message, InputNumber } from 'antd'
import FormItem from 'antd/es/form/FormItem'
import React, { useEffect } from 'react'
import { useMutation, useQuery } from 'react-query'
import classNames from 'classnames'
import { getRuleById, ruleSave } from '../../api/rule'
import { extractResponse } from '../../api/util'
import { useVersionContext } from '../../hooks/useVersion'

const CheckboxGroup = Checkbox.Group

//唯一性检查
const uniqueList = [
  { label: '重复图片审查', value: 'repeatImage' },
  { label: '空白图片审查', value: 'blank' },
]

//角度
const angleList = [
  { label: '文本方向', value: 'houseAngle' },
  { label: '倾斜', value: 'bias' },
]

// 格式检查
const formatList = [
  { label: 'JPEG', value: 'jpeg' },
  { label: 'JPG', value: 'jpg' },
  { label: 'TIFF', value: 'tiff' },
  { label: 'PDF', value: 'pdf' },
  // { label: 'GIF', value: 'gif' },
  // { label: 'RAW', value: 'raw' },
  // { label: 'BMP', value: 'bmp' },
  // { label: 'FPX', value: 'fpx' },
  // { label: 'PNG', value: 'png' },
]

//其他
const otherList = [
  { label: '空文件夹检查', value: 'blankFilesCheck' },
  { label: '篇幅统计', value: 'pageSize' },
  { label: '件号连续性检查', value: 'pieceContinuous' },
  { label: '图片页号连续性检查', value: 'continuity' },
]

const defaultValue: FilesDisposeType = {
  // 空白图片审查
  blank: true,
  // 重复图片审查
  repeatImage: true,
  // 角度
  // 文本方向
  houseAngle: true,
  // 倾斜
  bias: true,
  // 倾斜角容错
  rectify: '0.5',
  // 瑕疵
  flaw: true,
  // 黑边
  edgeRemove: true,
  // 污点容错
  stainValue: 10,
  // 装订孔容错
  hole: 1,
  // DPI检查
  dpi: true,
  // DPI值
  dpiValue: '300',
  // 格式检查
  format: true,
  formatList: ['jpeg', 'jpg'], // 'JPEG', 'JPEG' ,'TIFF', 'PDF', 'GIF',  'RAW','BMP', 'FPX', 'PNG'
  // 数量统计
  counting: true,
  // KB值检查
  kb: true,
  // KB最小值
  minKB: 10,
  // KB最大值
  maxKB: 1000,
  // 图像质量检查
  imageQuality:true,
  // 图像质量
  imageScore:75,
  // '空文件夹检查'
  blankFilesCheck: true,
  // 篇幅统计
  pageSize: true,
  // 条目数量图片一致性检查
  pieceContinuous: true,
  // 图片页号连续性检查
  continuity: true,
  // PDF文件质量审查
  // pdfImageUniformity: '',
  // 污点
  stain: true,
  bindingHole: true,
  ofdUniformity: false,
  reImageName:"",
  reImagePath:"",
  damage:true
}

const allValue = {
  // 空白图片审查
  blank: true,
  // 重复图片审查
  repeatImage: true,
  // 角度
  // 文本方向
  houseAngle: true,
  // 倾斜
  bias: true,
  // 倾斜角容错
  // 瑕疵
  flaw: true,
  // 黑边
  edgeRemove: true,
  // 污点容错
  // 装订孔容错
  hole: 1,
  // DPI检查
  dpi: true,
  // DPI值
  // 格式检查
  format: true,
  formatList: ['jpeg', 'jpg', 'tiff', 'pdf'], // 'JPEG', 'JPEG' ,'TIFF', 'PDF', 'GIF',  'RAW','BMP', 'FPX', 'PNG'
  // 数量统计
  counting: true,
  // KB值检查
  kb: true,
  // KB最小值
  minKB: 10,
  // KB最大值
  maxKB: 1000,
  // 图像质量检查
  imageQuality:true,
  // 图像分数
  imageScore:75,
  // '空文件夹检查'
  blankFilesCheck: true,
  // 篇幅统计
  pageSize: true,
  // 条目数量图片一致性检查
  pieceContinuous: true,
  // 图片页号连续性检查
  continuity: true,
  // PDF文件质量审查
  // pdfImageUniformity: '',
  // 污点
  stain: true,
  bindingHole: true,
  pdfImageUniformity: true,
  ofdUniformity: false,
  damage:true,
}

type FilesDisposeComType = {
  library: RuleMould
}

export default function FilesDispose({ library }: FilesDisposeComType) {
  const form = Form.useForm<FilesDisposeType>()[0]
  const { version } = useVersionContext()
  function xiChengText() {
    return version === 'xicheng' ? '!text-[22px]' : ''
  }
  const { data, refetch } = useQuery(
    ['getRuleById', library.id],
    extractResponse(() => getRuleById(library.id, library.type))
  )

  useEffect(() => {
    if (data && data[0]) {
      const json = JSON.parse(data[0].ruleValue!)
      console.log(json)
      json.imageQuality = json.imageQuality === undefined? defaultValue.imageQuality:json.imageQuality
      json.imageScore = json.imageScore === undefined? defaultValue.imageScore:json.imageScore
      json.damage = true
      form.setFieldsValue(json)
    } else {
      form.setFieldsValue(defaultValue)
    }
  }, [form, data])

  const ruleSaveMutation = useMutation(ruleSave, {
    onSuccess(res) {
      message.success(res.data.message)
      refetch()
    },
  })

  function submit(values: any) {
    ruleSaveMutation.mutate({
      libraryId: library.id,
      ruleValue: JSON.stringify(values),
      ruleType: library.type,
      id: data ? data[0]?.id : undefined,
      ruleCode: 'imageCheck',
    })
  }
  return (
    <Form
      form={form}
      onFinish={submit}
      className={classNames('mt-[54px]', xiChengText())}
    >
      <div className=" mb-2 flex">
        <div className="text-[#323233] font-bold py-0.5">唯一性检查</div>
        {data&&data[0]?.id?null:(<div className={"ml-4 bg-[rgb(245,34,45,0.1)] text-sm text-[rgb(245,34,45)] px-2 py-0.5"}>未保存</div>)}
      </div>
      <div className="flex">
        {uniqueList.map(item => (
          <FormItem name={item.value} className="mr-12" valuePropName="checked">
            <Checkbox>{item.label}</Checkbox>
          </FormItem>
        ))}
      </div>

      <div className=" text-[#323233] font-bold mb-2">角度</div>
      <div className="flex items-center">
        {angleList.map(item => (
          <FormItem name={item.value} className="mr-12" valuePropName="checked">
            <Checkbox>{item.label}</Checkbox>
          </FormItem>
        ))}
        <FormItem
          name="rectify"
          label="倾斜角容错"
          className={classNames('ml-4')}
        >
          <Input
            placeholder="0.0～3°"
            className="w-[88px] h-6"
            maxLength={40}
          />
        </FormItem>
      </div>

      <div className=" text-[#323233] font-bold mb-2">瑕疵</div>
      <div className="flex items-center">
        <FormItem name="edgeRemove" valuePropName="checked" className="mr-12">
          <Checkbox>黑边</Checkbox>
        </FormItem>
        <FormItem name="stain" valuePropName="checked" className="mr-4">
          <Checkbox> 污点</Checkbox>
        </FormItem>

        <FormItem name="stainValue" label="污点容错" className=" mr-24">
          <InputNumber
            placeholder="0"
            className="ml-2 !w-[80px] h-6"
            addonAfter={<div className="text-[#646566]">个</div>}
            max={40}
            size="small"
            controls={false}
          />
        </FormItem>
        <FormItem name="bindingHole" valuePropName="checked" className="">
          <Checkbox> 装订孔</Checkbox>
        </FormItem>

        <FormItem
          name="hole"
          label="装订孔容错"
          className=" ml-4 items-center "
        >
          <InputNumber
            placeholder="0"
            className="ml-2 !w-[80px] h-6 space-x-4"
            addonAfter={<div className="text-[#646566]">个</div>}
            size="small"
            controls={false}
          />
        </FormItem>
      </div>

      <div className=" text-[#323233] font-bold  mb-2">其他</div>
      <div className="flex items-center">
        <FormItem name="dpi" className="" valuePropName="checked">
          <Checkbox>DPI检查</Checkbox>
        </FormItem>
        <FormItem name="dpiValue" label="DPI值" className="ml-4">
          <InputNumber
            className="space-x-6 w-14"
            controls={false}
            size="small"
          />
        </FormItem>
      </div>
      <div className="flex items-center">
        <FormItem name="format" className="" valuePropName="checked">
          <Checkbox>格式检查</Checkbox>
        </FormItem>
        <FormItem name="counting" className="ml-12" valuePropName="checked">
          <Checkbox>数量统计</Checkbox>
        </FormItem>
      </div>
      <FormItem name="formatList" className="ml-8">
        <CheckboxGroup options={formatList} className="space-x-6" />
      </FormItem>
      <div className="flex items-center">
        <FormItem name="kb" className="" valuePropName="checked">
          <Checkbox>KB值检查</Checkbox>
        </FormItem>
        <FormItem name="minKB" label="最小值" className="ml-4 ">
          <InputNumber
            className="space-x-6 w-14"
            controls={false}
            size="small"
          />
        </FormItem>
        <FormItem name="maxKB" label="最大值" className="ml-4 ">
          <InputNumber
            className="space-x-6 w-14"
            controls={false}
            size="small"
          />
        </FormItem>
      </div>

      <div className="flex items-center">
        <FormItem name="imageQuality" className="" valuePropName="checked">
          <Checkbox>图像质量检查</Checkbox>
        </FormItem>
        <FormItem name="imageScore" label="最低图像质量" className="ml-4">
          <InputNumber
            className="space-x-6 w-14"
            controls={false}
            size="small"
            max={100}
            min={0}
          />
        </FormItem>
      </div>

      <div className="flex">
        {otherList.map(item => (
          <FormItem name={item.value} className="mr-12" valuePropName="checked">
            <Checkbox>{item.label}</Checkbox>
          </FormItem>
        ))}
      </div>

      <div className="flex items-center">
        <div >
          <FormItem
            name="reImagePath"
            label="图像路径(正则检查)"
          >
            <Input
              className="w-[88px] h-6"
            />
          </FormItem>
        </div>
        <div className={classNames('ml-4')}>
          <FormItem
          name="reImageName"
          label="图像名称(正则检查)"
        >
          <Input
            className="w-[88px] h-6"
          />
        </FormItem>
        </div>
      </div>

      <div>
        <FormItem
          name="damage"
          valuePropName="checked"
        >
          <Checkbox  disabled={true}>破损文件检查</Checkbox>
        </FormItem>
      </div>

      <div className=" text-[#323233] font-bold mt-6 mb-2">PDF文件质量审查</div>
      <div className="flex">
        <FormItem
          name="pdfImageUniformity"
          className=""
          valuePropName="checked"
        >
          <Checkbox>PDF与图片一致性审查</Checkbox>
        </FormItem>
        <FormItem name="ofdUniformity" className="" valuePropName="checked">
          <Checkbox>OFD一致性检查</Checkbox>
        </FormItem>
      </div>

      <Button type="primary" onClick={form.submit}>
        {data && data?.length > 0 ? '确定' : '保存'}
      </Button>
      <Button
        type="default"
        className="ml-4"
        onClick={() => {
          const obj = form.getFieldsValue()
          form.setFieldsValue({ ...obj, ...allValue })
        }}
      >
        全选
      </Button>
    </Form>
  )
}
