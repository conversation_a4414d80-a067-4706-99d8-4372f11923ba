import React, { useEffect, useState } from 'react'
import { Checkbox, Input, Modal, Select } from 'antd'
import { MinusCircleOutlined } from '@ant-design/icons'
import { UseMutationResult, useQuery } from 'react-query'
import { AxiosResponse } from 'axios'
import FieldLibraryIcon from '../icons/FieldLibraryIcon'
import WayLineIcon from '../icons/WayLineIcon'
import ConnectLineIcon from '../icons/ConnectLineIcon'
import { getFieldPage } from '../../api/fieldLibrary'
import { extractResponse } from '../../api/util'
import { PageData, Result } from '../../api/types'

type MateSettingType = {
  open: boolean
  close: React.Dispatch<React.SetStateAction<boolean>>
  activeConfig?: CatalogueFile
  projectConfigSaveMutation: UseMutationResult<
    AxiosResponse<Result<PageData<ProjectType>>, any>,
    unknown,
    CatalogueFile,
    unknown
  >
  libraryId: string
}

const toolList = [
  {
    label: '字段',
    type: 'field',
    icon: (
      <FieldLibraryIcon className=" w-4 h-4 fill-current text-opacity-[.447] text-[#000]" />
    ),
  },
  {
    label: '路径符',
    type: 'way',
    icon: (
      <WayLineIcon className=" w-4 h-4 fill-current text-opacity-[.447] text-[#000]" />
    ),
  },
  {
    label: '连接符',
    type: 'connect',
    icon: (
      <ConnectLineIcon className=" w-4 h-4 fill-current text-opacity-[.447] text-[#000]" />
    ),
  },
  // {
  //   label: '件号',
  //   type: 'piece',
  //   icon: (
  //     <ConnectLineIcon className=" w-4 h-4 fill-current text-opacity-[.447] text-[#000]" />
  //   ),
  // },
]

type mateType = {
  type: string // field字段 way路径 connect连接
  label: string
  values: string
}

export default function MateSetting({
  open,
  close,
  activeConfig,
  projectConfigSaveMutation,
  libraryId,
}: MateSettingType) {
  const [mateList, setMateList] = useState<mateType[]>([])
  const [previewText, setPreviewText] = useState('')
  const [pieceHaveIs, setPieceHaveIs] = useState(0)

  useEffect(() => {
    if (open) {
      if (activeConfig && activeConfig.urlRule) {
        const list = JSON.parse(activeConfig.urlRule)
        setMateList(list)
        setPieceHaveIs(activeConfig.pieceHaveIs)
      }
    } else {
      setMateList([])
    }
  }, [activeConfig, open])

  const { data } = useQuery(
    ['getFieldPage', activeConfig?.libraryId],

    extractResponse(() =>
      getFieldPage(1, 99999999, {
        search_EQ_libraryId: libraryId,
      })
    ),

    {
      onSuccess(res) {
        console.log(res?.list)
      },
      enabled: open,
    }
  )

  function matePreview() {
    let text = ''
    mateList.forEach(item => {
      text += item.label
    })
    setPreviewText(text)
  }

  useEffect(() => {
    matePreview()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [mateList])

  return (
    <Modal
      title="档号规则"
      open={open}
      onCancel={() => close(false)}
      onOk={() => {
        projectConfigSaveMutation.mutate({
          ...activeConfig!,
          urlRule: JSON.stringify(mateList),
          pieceHaveIs: pieceHaveIs,
        })
        close(false)
      }}
    >
      <div className="flex items-top">
        <div>
          <div className=" w-36 h-72 border px-4 pt-5 rounded">
            {toolList.map(tool => (
              <div
                className="flex items-center mb-4 cursor-pointer"
                onClick={() => {
                  let obj: mateType
                  if (tool.type === 'way') {
                    obj = { type: tool.type, label: '/', values: '/' }
                    // } else if (tool.type === 'piece') {
                    //   obj = { type: tool.type, label: '件号', values: '' }
                  } else {
                    obj = { type: tool.type, label: '', values: '' }
                  }
                  setMateList(p => [...p, obj])
                }}
              >
                <div>{tool.icon}</div>
                <div className="ml-2">{tool.label}</div>
              </div>
            ))}
          </div>
          <div>
            <Checkbox
              className="text-xs mt-5"
              checked={!!pieceHaveIs}
              onChange={e => {
                setPieceHaveIs(e.target.checked ? 1 : 0)
              }}
            >
              档号中是否包含件号
            </Checkbox>
          </div>
        </div>

        <div className="ml-6 ">
          <div className=" w-[304px] h-72 border p-4 overflow-y-auto">
            {mateList.map((mate, index) => {
              let dom = null
              if (mate.type === 'field') {
                dom = (
                  <Select
                    className="!w-[223px] h-8"
                    options={data?.list}
                    fieldNames={{ label: 'fieldName', value: 'id' }}
                    value={mate.values}
                    onChange={(
                      item: string,
                      options: FieldType | FieldType[]
                    ) => {
                      const list = [...mateList]
                      list[index].values = item
                      if (!(options instanceof Array)) {
                        list[index].label = options.fieldName
                      }
                      setMateList(list)
                    }}
                  />
                )
              } else if (mate.type === 'connect') {
                dom = (
                  <Input
                    className="!w-[223px] h-8"
                    value={mate.label}
                    onChange={e => {
                      const list = [...mateList]
                      list[index].values = e.target.value
                      list[index].label = e.target.value
                      setMateList(list)
                    }}
                    maxLength={40}
                  />
                )
              } else if (mate.type === 'way') {
                dom = (
                  <div className="!w-[223px] h-8 border border-opacity-10 flex items-center pl-4">
                    /
                  </div>
                )
              } else {
                dom = (
                  <div className="!w-[223px] h-8 border border-opacity-10 flex items-center pl-4">
                    件号
                  </div>
                )
              }
              return (
                <div className="flex items-center mb-4">
                  {dom}
                  <MinusCircleOutlined
                    className="ml-4 cursor-pointer"
                    onClick={() => {
                      const list = [...mateList]
                      list.splice(index, 1)
                      setMateList(list)
                    }}
                  />
                </div>
              )
            })}
          </div>
          <div className="mt-4 flex items-center">
            预览：
            <div className=" bg-[#F7F7F7] w-[262px] min-h-[32px] pt-0.5 pl-4">
              {previewText}
            </div>
          </div>
        </div>
      </div>
    </Modal>
  )
}
