const path = require('path')
const fs = require('fs')
const cheerio = require('cheerio')
const dotenv = require('dotenv')

const envFilePath = path.resolve(__dirname, '.env.local')

if (!fs.existsSync(envFilePath)) {
  throw new Error('请在根目录创建.env.local文件，添加JAVA_PROJECT_PATH')
}

dotenv.config({ path: envFilePath })

if (!process.env.JAVA_PROJECT_PATH) {
  throw new Error('没有拿到java目录：添加JAVA_PROJECT_PATH')
}

const JavaSourceFolder = process.env.JAVA_PROJECT_PATH

const JavaResourcesFolder = path.resolve(
  JavaSourceFolder,
  'kpass-exactness-check/kpass-exactness-start/src/main/resources'
)
const JavaTemplatesFolder = path.resolve(JavaResourcesFolder, 'templates')

function writeTemplate(script) {
  const sourceTemplateHtmlPath = path.resolve(JavaTemplatesFolder, 'index.html')
  fs.readFile(sourceTemplateHtmlPath, (readError, data) => {
    if (!readError) {
      const $ = cheerio.load(data.toString())
      $('body').html(`
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    ${script}
`)
      fs.writeFile(
        sourceTemplateHtmlPath,
        $.html(),
        { encoding: 'utf-8' },
        writeError => {
          if (!writeError) {
            console.log(`html模板写入至：${sourceTemplateHtmlPath}`)
          }
        }
      )
    }
  })
}

function removeFiles(folderName, callback = () => {}) {
  const javaAssetsPath = path.resolve(JavaResourcesFolder, 'static', folderName)
  fs.readdir(javaAssetsPath, (readDirError, files) => {
    if (!readDirError) {
      let removeNo = 0
      if (files.length) {
        files.forEach(filename => {
          fs.unlink(path.join(javaAssetsPath, filename), removeError => {
            if (!removeError) {
              removeNo++
              if (removeNo === files.length) {
                callback(javaAssetsPath)
              }
            } else {
              console.log(`删除文件：${filename} 失败`)
            }
          })
        })
      } else {
        callback(javaAssetsPath)
      }
    } else {
      console.log(readDirError)
    }
  })
}

function copyAssets(folderName, callback = () => {}) {
  const distAssetsPath = path.resolve(__dirname, 'dist', folderName)
  console.log('distAssetsPath', distAssetsPath)
  fs.readdir(distAssetsPath, (readDirError, files) => {
    if (!readDirError) {
      let copyNo = 0
      if (files.length) {
        files.forEach(filename => {
          const filePath = path.resolve(distAssetsPath, filename)
          fs.copyFile(
            filePath,
            path.resolve(JavaResourcesFolder, 'static', folderName, filename),
            copyError => {
              if (!copyError) {
                copyNo++
                if (copyNo === files.length) {
                  callback(distAssetsPath)
                }
              } else {
                console.log(`拷贝文件：${filename} 失败`)
              }
            }
          )
        })
      } else {
        console.log('没有最新的静态资源')
      }
    } else {
      console.log(readDirError)
    }
  })
}

function replaceStaticFile() {
  const f2eStaticPath = path.resolve(__dirname, 'static')
  fs.readdir(f2eStaticPath, (err, files) => {
    if (!err) {
      files.forEach(filename => {
        fs.copyFile(
          path.resolve(f2eStaticPath, filename),
          path.resolve(JavaResourcesFolder, 'static', filename),
          copyErr => {
            if (!copyErr) {
              console.log('拷贝前端本地资源成功')
            }
          }
        )
      })
    }
  })
}

function release(script) {
  replaceStaticFile()
  removeFiles('assets', removeAssetsPath => {
    console.log('删除java项目历史文件成功', removeAssetsPath)
    copyAssets('assets', copyAssetsPath => {
      console.log('拷贝前端项目最新资源成功', copyAssetsPath)
      writeTemplate(script)
    })
  })
}

module.exports = release
