import React, { useEffect, useState } from 'react'
import { useLocation } from 'react-router-dom'

export default function Layout({ children }: React.PropsWithChildren<any>) {
  const [isLayout, setIsLayout] = useState(false)
  const location = useLocation()
  useEffect(() => {
    location.pathname === '/login' ? setIsLayout(false) : setIsLayout(true)
  }, [location.pathname])
  return (
    <div>
      {isLayout ? <div className="h-14 bg-white" /> : null}
      {children}
    </div>
  )
}
