import React from 'react'

import { fetchImage, getInitialImageSize } from '../../utils/image'
import { getLocalStorage } from '../../utils'
import math from '../../utils/math'
import {
  CATALOG_ZOOM_KEY,
  CATALOG_ZOOM_CURRENT,
  CATALOG_CHECK_KEY,
} from '../../constants'

interface Props {
  isOpen?: boolean
  rotate: number
  image?: HTMLImageElement
  canvasRef: React.RefObject<HTMLCanvasElement>
  containerRef: React.RefObject<HTMLDivElement>
  activeImageIndex?: number
  imageList: ImageType[]
}

function getZoom() {
  const localZoom = getLocalStorage(CATALOG_ZOOM_KEY)
  return localZoom ? Number(localZoom) : CATALOG_ZOOM_CURRENT
}

function useCutImage({
  isOpen,
  rotate,
  canvasRef,
  containerRef,
  activeImageIndex,
  imageList,
}: Props) {
  const [image, setImage] = React.useState<HTMLImageElement>()
  const [zoom, setZoom] = React.useState(getZoom)
  const [ratio, setRatio] = React.useState(0)
  const [originalSize, setOriginalSize] = React.useState<{
    width: number
    height: number
  }>()

  const [canvasStyle, setCanvasStyle] = React.useState<{
    width: number
    height: number
  }>()
  const [boxStyle, setBoxStyle] = React.useState<{
    width: number
    height: number
  }>()
  React.useEffect(() => {
    if (isOpen) {
      setZoom(getZoom())
    }
  }, [isOpen])

  React.useEffect(() => {
    const unsubscribe: (() => any)[] = []
    setImage(undefined)
    if (
      typeof activeImageIndex === 'number' &&
      imageList.length > activeImageIndex
    ) {
      const image = imageList[activeImageIndex]
      const [task, canceler = () => {}] = fetchImage(
        `/api/exactness/tool/${image.value}`
      )
      task.then(image => {
        console.log(image)
        setImage(image)
      })

      unsubscribe.push(canceler)
    }

    return () => {
      unsubscribe.map(canceler => canceler?.())
    }
  }, [activeImageIndex, imageList])

  React.useEffect(() => {
    if (image) {
      const width = rotate === 0 || rotate === 2 ? image.width : image.height
      const height = rotate === 0 || rotate === 2 ? image.height : image.width
      setOriginalSize({ width, height })
    } else {
      setOriginalSize(undefined)
    }
  }, [image, rotate])

  React.useEffect(() => {
    const container = containerRef.current
    if (originalSize && container) {
      const rect = container.getBoundingClientRect()
      const box = getInitialImageSize(originalSize.width, originalSize.height, {
        width: rect.width,
        height: rect.height,
      })

      setBoxStyle(box)
      const ratio = math.init(originalSize.width, 2).divide(box.width).end()
      setRatio(ratio)
      const localValue = String(getLocalStorage(CATALOG_CHECK_KEY))
      if (localValue === '0') {
        setZoom(100)
      } else {
        setZoom(getZoom())
      }
    }
  }, [containerRef, originalSize])
  React.useEffect(() => {
    const canvas = canvasRef.current
    if (!isOpen || !canvas || !image || !ratio || !boxStyle) {
      return
    }

    const width = rotate === 0 || rotate === 2 ? image.width : image.height
    const height = rotate === 0 || rotate === 2 ? image.height : image.width
    canvas.width = width
    canvas.height = height
    console.log({ boxStyle })
    canvas.style.cssText = [
      `width: ${math
        .init(boxStyle.width, 2)
        .multiply(zoom / 100)
        .end()}px`,
      `height: ${math
        .init(boxStyle.height, 2)
        .multiply(zoom / 100)
        .end()}px`,
      'cursor: crosshair',
    ].join(';')
    setCanvasStyle({
      width: math
        .init(boxStyle.width, 2)
        .multiply(zoom / 100)
        .end(),
      height: math
        .init(boxStyle.height, 2)
        .multiply(zoom / 100)
        .end(),
    })

    const context = canvas.getContext('2d') as CanvasRenderingContext2D

    function paintImage(sm: HTMLImageElement) {
      context.fillStyle = 'white'
      context.rect(0, 0, width, height)
      context.fill()
      if (rotate !== 0) {
        context.translate(width / 2, height / 2)
        context.rotate((rotate * Math.PI) / 2)
        context.drawImage(sm, sm.width / -2, sm.height / -2)
        context.resetTransform()
      } else {
        context.drawImage(sm, 0, 0)
      }
    }

    paintImage(image)
  }, [
    rotate,
    isOpen,
    image,
    canvasRef,
    containerRef,
    ratio,
    zoom,
    boxStyle?.width,
    boxStyle?.height,
    boxStyle,
  ])
  return {
    zoom,
    setZoom,
    originalSize,
    canvasStyle,
  }
}

export default useCutImage
