/* ./your-css-folder/styles.css */


/* @font-face {
  font-family: 'myFamily';
  src:url('../style/font/SourceHanSansK-Medium.ttf') format('truetype'),
} */

* {
  font-family: myFamily
}

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  vertical-align: baseline;
}

img {
  display: inline;
}

body,
html {
  background-color: #F7F7F7 !important;
}

.classification>.ant-collapse-header {
  padding: 5px 16px !important;
  background-color: #F5F5F5;
}

.classification>.ant-collapse-content {
  background-color: white !important;
}

.classification>.ant-collapse-content>.ant-collapse-content-box {
  padding: 0 !important;
}


.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected {
  background-color: rgba(255, 119, 13, 0.1) !important;
}

.css-dev-only-do-not-override-12jzuas.ant-pagination .ant-pagination-item-active {
  background-color: #ffffff;
  border-color: #FF770d !important;
}

.css-dev-only-do-not-override-12jzuas.ant-pagination .ant-pagination-item-active a {
  color: #FF770d !important;

}


.tran-h.ant-transfer .ant-transfer-list {
  height: 232px !important;
  width: 212px !important;
}

.mb-6.ant-transfer{
  margin-bottom: 24px !important;
}