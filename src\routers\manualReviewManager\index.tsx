import { Button, Input, Table, message } from 'antd'
import { ColumnsType } from 'antd/lib/table'
import React, { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useQuery } from 'react-query'

import UserSelect from '../../components/UserSelect'
import { getTaskInfoPage } from '../../api/project'
import { extractResponse } from '../../api/util'
import ExamineAdd from '../../components/manualReviewManager/ExamineAdd'
import { useUserContext } from '../../hooks/useUser'

const reg = /^(?:\d|[1-9]\d|100)(?:\.\d{1,2})?$/
const regs = /^-?\d+$/

export default function ProjectManage() {
  const navigate = useNavigate()
  const [param, setParam] = useState<{
    pageNo: number
    pageSize: number
    search_CONTAINS_taskName: string
    search_EQ_createUserId: string | undefined
    search_EQ_createTime: string
    search_GTE_manualRatio: number | undefined
    search_LTE_manualRatio: number | undefined
    search_GTE_userCheckCount: number | undefined
    search_LTE_userCheckCount: number | undefined
  }>({
    pageNo: 1,
    pageSize: 10,
    search_CONTAINS_taskName: '',
    search_EQ_createUserId: undefined,
    search_EQ_createTime: '',
    search_GTE_manualRatio: undefined,
    search_LTE_manualRatio: undefined,
    search_GTE_userCheckCount: undefined,
    search_LTE_userCheckCount: undefined,
  })

  const [activeTask, setActiveTask] = useState<ProjectType>()
  const [taskList, setTaskList] = useState<ProjectType[]>([])
  const { user } = useUserContext()

  // const { data: allocationData, refetch } = useQuery(
  //   ['allocation', projectId],
  //   projectId ? extractResponse(() => allocation(projectId)) : () => undefined,
  //   {
  //     enabled: !!projectId,
  //     onSuccess(res) {},
  //     onError(res) {
  //       navigate(-1)
  //     },
  //   }
  // )
  console.log({ param })
  const columns: ColumnsType<ProjectType> = [
    {
      title: '任务名称',
      render: item => <div>{item.taskName}</div>,
      key: 'taskName',
    },

    {
      title: '人工进度',
      render: (item: ProjectType) => (
        <div className="flex items-center">
          <div className="ml-2">{item.userCheckProgress || 0} %</div>
          {item.userCheckProgress === 100 ? (
            <Button
              href={'/api/exactness/userCheck/reportPdf?taskId=' + item.id}
              className="ml-4 !text-primary-default"
              type="link"
            >
              导出报告
            </Button>
          ) : null}
        </div>
      ),
      key: 'manProgress',
    },
    {
      title: '抽查比例',
      render: (item: ProjectType) => <div>{item.manualRatio || 0} %</div>,
    },
    {
      title: '抽查卷数',
      render: (item: ProjectType) => <div>{item.userCheckCount || 0} </div>,
    },
    {
      title: '创建人',
      render: (item: ProjectType) => <div>{item.createUserName}</div>,
      key: 'createUserName',
    },
    {
      title: '创建时间',
      render: (item: ProjectType) => <div>{item.createTime}</div>,
      key: 'createTime',
    },
    {
      title: '操作',
      render: (item: ProjectType) => {
        return (
          <div className="flex text-primary-default">
            {user?.aidosRole === 'admin' || user?.aidosRole === 'superAdmin' ? (
              <div
                className=" cursor-pointer mr-6"
                onClick={() => {
                  setActiveTask(item)
                }}
              >
                添加审核
              </div>
            ) : null}
            {item.isShow !== 1 && user?.aidosRole === 'user' ? null : (
              <div
                className=" cursor-pointer"
                onClick={() => {
                  navigate(
                    '/manualReviewManager/projectAllocation?id=' + item.id
                  )
                }}
              >
                查看详情
              </div>
            )}
          </div>
        )
      },
      key: 'operate',
    },
  ]

  const { data, refetch } = useQuery(
    ['getTaskInfoPage'],
    extractResponse(() =>
      getTaskInfoPage(param.pageNo, param.pageSize, {
        ...param,
      })
    ),
    {
      refetchInterval: taskList.find(item => item.taskStatus === 1)
        ? 1000
        : false,
      onSuccess(res) {
        setTaskList(res.list)
      },
    }
  )

  useEffect(() => {
    refetch()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [param.pageNo])

  const paginationObj = {
    total: data?.total,
    showTotal: (total: any, range: any) =>
      `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
    defaultCurrent: 1,
    defaultPageSize: 10,
    onChange: (page: number) => {
      setParam(p => ({ ...p, pageNo: page }))
    },
    showSizeChanger: false,
    onShowSizeChange: (current: any, size: number) => {
      setParam(p => ({ ...p, pageSize: size }))
    },
    showQuickJumper: true,
    current: data?.pageNo,
  }

  return (
    <div>
      <div className="flex justify-between px-4 items-center h-16 text-base">
        <div className="w-16 mr-4">人工审查</div>
        <div className="flex">
          <Input
            placeholder="任务名称"
            className="!mr-4 !w-[200px]"
            value={param.search_CONTAINS_taskName}
            onChange={e => {
              setParam(p => ({
                ...p,
                search_CONTAINS_taskName: e.target.value,
              }))
            }}
            allowClear={true}
            maxLength={40}
          />

          <UserSelect
            className="!mr-4 !w-[200px]"
            value={param.search_EQ_createUserId}
            onChange={e => {
              setParam(p => ({ ...p, search_EQ_createUserId: e }))
            }}
          />
          <Input
            placeholder="抽查比例（起始值）"
            className=" !w-[200px] !rounded-r-none !border-r-0"
            value={param.search_GTE_manualRatio || ''}
            onChange={e => {
              const value = Number(e.target.value)
              if (reg.test(String(value))) {
                setParam(p => ({
                  ...p,
                  search_GTE_manualRatio: value,
                }))
              }
            }}
            maxLength={40}
          />

          <Input
            placeholder="抽查比例（结尾值）"
            className="!mr-4 !w-[200px] !rounded-l-none"
            value={param.search_LTE_manualRatio || ''}
            onChange={e => {
              const value = Number(e.target.value)
              if (reg.test(String(value))) {
                setParam(p => ({
                  ...p,
                  search_LTE_manualRatio: value,
                }))
              }
            }}
          />

          <Input
            placeholder="抽查卷数（起始值）"
            className=" !w-[200px] !rounded-r-none !border-r-0"
            value={param.search_GTE_userCheckCount || ''}
            onChange={e => {
              const value = Number(e.target.value)
              if (regs.test(String(value))) {
                console.log({ value })
                setParam(p => ({
                  ...p,
                  search_GTE_userCheckCount:
                    e.target.value === '' ? undefined : value,
                }))
              }
            }}
          />

          <Input
            placeholder="抽查卷数（结尾值）"
            className="!mr-4 !w-[200px] !rounded-l-none"
            value={param.search_LTE_userCheckCount || ''}
            onChange={e => {
              const value = Number(e.target.value)
              if (regs.test(String(value))) {
                setParam(p => ({
                  ...p,
                  search_LTE_userCheckCount:
                    e.target.value === '' ? undefined : value,
                }))
              }
            }}
          />

          <Button
            className="mr-4"
            onClick={() => {
              console.log({
                结束: param.search_LTE_manualRatio,
                开始: param.search_GTE_manualRatio,
              })
              if (
                param.search_LTE_manualRatio &&
                param.search_GTE_manualRatio &&
                param.search_LTE_manualRatio < param.search_GTE_manualRatio
              ) {
                message.warning('抽查比例范围有误')
                return
              }
              if (
                param.search_LTE_userCheckCount &&
                param.search_GTE_userCheckCount &&
                param.search_LTE_userCheckCount <
                  param.search_GTE_userCheckCount
              ) {
                message.warning('抽查卷数范围有误')
                return
              }
              refetch()
            }}
          >
            搜索
          </Button>
        </div>
      </div>
      <div>
        <Table
          dataSource={data ? data.list : []}
          columns={columns}
          pagination={paginationObj}
          className="mx-4"
          rowKey={record => record.id!}
        />
      </div>
      <ExamineAdd
        activeTask={activeTask}
        close={() => {
          setActiveTask(undefined)
        }}
      />
    </div>
  )
}
