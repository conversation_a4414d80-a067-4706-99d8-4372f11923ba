// 匹配检查校验
export function mateCheckVerification(map: Record<string, string>) {
  for (const key in map) {
    if (!map[key]) {
      return { label: '匹配检查有字段未匹配', error: true }
    }
  }
  return { label: '', error: false }
}
// 任务详情
// export function configVerification(configList: CatalogueFile[]) {
//   let obj = { label: '', error: false }
//   configList.forEach(config => {
//     if (
//       !config.ruleConfigType ||
//       !config.urlRule ||
//       !config.ruleTemplateId ||
//       !config.filePath
//     ) {
//       obj = { label: '目录文件未完全配置', error: true }
//       return
//     }
//     if (
//       (config.ruleConfigType === 1 || config.ruleConfigType === 0) &&
//       !config.sheetName
//     ) {
//       obj = { label: '目录文件未完全配置', error: true }
//       return
//     }
//     if (config.ruleConfigType === 1 || config.ruleConfigType === 0) {
//       if (!config.sheetName) {
//         obj = { label: '目录文件未完全配置', error: true }
//         return
//       }
//       if (!config.ruleMapping) {
//         obj = { label: '目录文件未完全配置', error: true }
//         return
//       }

//       if (mateCheckVerification(JSON.parse(config.ruleMapping))) {
//         obj = { label: '目录文件未完全配置', error: true }
//       }
//     }
//   })
//   return obj
// }
