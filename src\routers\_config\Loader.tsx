import { Empty } from 'antd'
import React, {
  ComponentPropsWithRef,
  ComponentType,
  lazy,
  Suspense,
} from 'react'
import { RouteObject } from 'react-router-dom'

export function lazyRoute<T extends ComponentType<any>>(
  factory: () => Promise<{ default: T }>,
  props?: ComponentPropsWithRef<T>
) {
  const LazyComponent = lazy(factory)
  return (
    <Suspense fallback={null}>
      <LazyComponent {...(props as ComponentPropsWithRef<T>)} />
    </Suspense>
  )
}

export const emptyRoute: RouteObject = {
  path: '*',
  element: <Empty />,
}
