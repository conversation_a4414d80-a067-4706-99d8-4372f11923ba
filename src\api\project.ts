// save

import { AxiosRequestConfig } from 'axios'
import request from './requestExactness'
import { PageData, Result } from './types'

export function taskInfoSave(formData: ProjectBindDataDO) {
  return request.post<Result<ProjectType>>('/taskInfo/save', formData)
}

export function getTaskInfoPage(
  pageNo: number,
  pageSize: number,
  params: Record<string, any>
) {
  const param = JSON.parse(JSON.stringify(params))
  for (const item in param) {
    if (param[item] === '') {
      delete params[item]
    }
  }
  delete params.pageNo
  delete params.pageSize

  return request.post<Result<PageData<ProjectType>>>('/taskInfo/page', {
    pageNo,
    pageSize,
    sortInfo: 'DESC_createTime',
    param: params,
  })
}

// /exactness/userCheck

export function getUserCheckPage(
  pageNo: number,
  pageSize: number,
  params: Record<string, any>
) {
  const param = JSON.parse(JSON.stringify(params))
  for (const item in param) {
    if (param[item] === '') {
      delete params[item]
    }
  }
  delete params.pageNo
  delete params.pageSize

  return request.post<Result<PageData<UserCheckType>>>('/userCheck/page', {
    pageNo,
    pageSize,
    sortInfo: 'DESC_createTime',
    param: params,
  })
}

export function taskInfoDelById(id: string) {
  return request.get<Result<string>>('/taskInfo/delById?id=' + id)
}

export function serverUpload(params: {
  url: string
  filePath: string
  taskConfigId: string
  rule?: string
}) {
  return request.post<Result<PageData<ProjectType>>>(
    '/taskUpload' + params.url,
    {
      filePath: params.filePath,
      taskConfigId: params.taskConfigId,
      rule: params.rule,
    }
  )
}

export function taskConfigGetByTaskId(taskId: string) {
  return request.get<Result<CatalogueFile[]>>(
    '/taskConfig/getByTaskId?taskId=' + taskId
  )
}

export function projectConfigSave(params: CatalogueFile) {
  return request.post<Result<PageData<ProjectType>>>('/taskConfig/save', params)
}

export function projectConfigDelById(id: string) {
  return request.get<Result<FieldLibraryType[]>>('/taskConfig/delById?id=' + id)
}

// getSheets
export function getSheets(path: string) {
  return request.get<Result<CatalogueFile[]>>('/tool?path=' + path)
}

// getHeards

export function getHeards(param: { path: string; sheetName: string }) {
  return request.post<Result<string[]>>(`/tool/getHeards`, param)
}

// statistics

export function getStatistics(taskId: string) {
  return request.get<Result<ImageStatisticType[]>>(
    '/taskError/result/statistics?taskId=' + taskId
  )
}

// statisticsImage

export function statisticsImage(taskId: string) {
  return request.get<Result<ImageStatisticType[]>>(
    '/taskError/result/statisticsImage?taskId=' + taskId
  )
}
export function statisticsRule(taskId: string, taskConfigId: string) {
  return request.get<Result<ImageStatisticType[]>>(
    `/taskError/result/statisticsRule?taskId=${taskId}&taskConfigId=${taskConfigId}`
  )
}

export function statisticsLogicRule(taskId: string, taskConfigId: string) {
  return request.get<Result<ImageStatisticType[]>>(
    `/taskError/result/statisticsLogicRule?taskId=${taskId}&taskConfigId=${taskConfigId}`
  )
}

// /work
export function workStart(taskId: string) {
  return request.get<Result<CatalogueFile[]>>('/work/start?taskId=' + taskId)
}
export function workStop(taskId: string) {
  return request.get<Result<CatalogueFile[]>>('/work/stop?taskId=' + taskId)
}

// 继续
export function workContinue(taskId: string) {
  return request.get<Result<CatalogueFile[]>>('/work/continue?taskId=' + taskId)
}

// 暂停
export function workSuspend(taskId: string) {
  return request.get<Result<CatalogueFile[]>>('/work/suspend?taskId=' + taskId)
}

export function taskInfoGetById(id: string) {
  return request.get<Result<ProjectType>>('/taskInfo/getById?id=' + id)
}

export function getErrorImageDetail(param: {
  taskId: string
  imageName: string
}) {
  return request.post<Result<ImageRuleInfoType[]>>(
    '/taskError/result/errorImageDetail',
    param
  )
}

// taskData/mateImage
export function mateImage(
  pageNo: number,
  pageSize: number,
  params: Record<string, any>
) {
  delete params.pageNo
  delete params.pageSize

  return request.post<Result<PageData<Record<string, string>>>>(
    '/taskData/mateImage',
    {
      pageNo,
      pageSize,
      // sortInfo: 'DESC_createTime',
      param: params,
    }
  )
}

export function mateExcel(
  pageNo: number,
  pageSize: number,
  params: Record<string, any>
) {
  const param = JSON.parse(JSON.stringify(params))
  for (const item in param) {
    if (param[item] === '') {
      delete params[item]
    }
  }
  delete params.pageNo
  delete params.pageSize

  return request.post<Result<PageData<Record<string, string>>>>(
    '/taskData/mateExcel',
    {
      pageNo,
      pageSize,
      // sortInfo: 'DESC_createTime',
      param: params,
    }
  )
}

//

export function allocation(taskId: string) {
  return request.get<Result<UserCheckType>>(
    '/userCheck/allocation?taskId=' + taskId
  )
}

// getById

export function allocationGetById(id: string) {
  return request.get<Result<UserCheckType>>('/userCheck/getById?id=' + id)
}

export function getImages(taskId: string, dataKey: string) {
  return request.get<Result<PieceImageType[]>>(
    `/userCheck/getImages?taskId=${taskId}&dataKey=${dataKey}`
  )
}

export function getPieces(taskId: string, dataKey: string) {
  return request.get<Result<FieldInspectType[]>>(
    `/taskData/getPieces?taskId=${taskId}&dataKey=${dataKey}`
  )
}

export function getFile(taskId: string, dataKey: string) {
  return request.get<Result<FieldInspectType>>(
    `/taskData/getFile?taskId=${taskId}&dataKey=${dataKey}`
  )
}

export function getCount(taskId: string, dataKey: string) {
  return request.get<Result<{ ok: number; error: number }>>(
    `/userCheck/getCount?taskId=${taskId}&dataKey=${dataKey}`
  )
}

// /save

export function userCheckSave(param: UserCheckType) {
  return request.post<Result<ImageRuleInfoType[]>>('/userCheck/save', param)
}

export function userCheckSaves(param: UserCheckType[]) {
  return request.post<Result<ImageRuleInfoType[]>>('/userCheck/saves', param)
}

export function taskDataSaves(param:TaskDataType){
  return request.post<Result<ImageRuleInfoType[]>>('/taskData/save', param)
}

// taskInfo/getLibraryByTaskId

export function getLibraryByTaskId(taskId: string) {
  return request.get<Result<FieldType[]>>(
    `/taskInfo/getLibraryByTaskId?taskId=${taskId}`
  )
}

export function scanUpload(
  activeConfig: CatalogueFile,
  formData: FormData,
  config?: AxiosRequestConfig,
  url?: string
) {
  return request.post<Result<string>>(
    `/taskUpload/${url}?taskConfigId=${activeConfig?.id}`,
    formData,
    config
  )
}

// api/exactness/tool/getAppVersion

export function getAppVersion() {
  return request.get<Result<string>>(`/tool/getAppVersion`)
}

// saveFieldSpecialLayout

export type SaveFieldLayoutType = {
  layout: string
  taskId: string
  typeId: string
  id?: string
}

export function saveFieldLayout(param: SaveFieldLayoutType) {
  return request.post<Result<ImageRuleInfoType[]>>('/layout/save', param)
}

export function getLayout(taskId: string, typeId: string) {
  return request.get<Result<SaveFieldLayoutType>>(
    `/layout/getLayout?taskId=${taskId}&typeId=${typeId}`
  )
}

// UserCheckGroupGroup

export type UserCheckGroupType = {
  taskId: string
  userCheckId: string
  userCheckName: string
}

export function userCheckGroupSaves(param: UserCheckGroupType[]) {
  return request.post<Result<ImageRuleInfoType[]>>(
    '/UserCheckGroupGroup/saves',
    param
  )
}

export function userCheckGroupDelTaskId(taskId: string) {
  return request.get<Result<UserCheckGroupType[]>>(
    `/UserCheckGroupGroup/delTaskId?taskId=${taskId}`
  )
}

//

export function userCheckGroupGetByTaskId(taskId: string) {
  return request.get<Result<UserCheckGroupType[]>>(
    `/UserCheckGroupGroup/getByTaskId?taskId=${taskId}`
  )
}
