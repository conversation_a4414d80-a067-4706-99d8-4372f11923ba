import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  PlusOutlined,
  SyncOutlined,
} from '@ant-design/icons'
import { Button, Input, Modal, message } from 'antd'
import React, { useEffect, useState } from 'react'
import classNames from 'classnames'
import Table, { ColumnsType } from 'antd/lib/table'
import { useMutation, useQuery } from 'react-query'
import { debounce } from 'lodash-es'
import LibraryAdd from '../../components/FieldLibrary/LibraryAdd'
import FieldAdd from '../../components/FieldLibrary/FieldAdd'
import UploadLibrary from '../../components/FieldLibrary/UploadLibrary'
import UserSelect from '../../components/UserSelect'
import {
  fieldDelByIds,
  getFieldPage,
  libraryDelById,
  libraryTreeVo,
} from '../../api/fieldLibrary'
import { extractResponse } from '../../api/util'
import DeleteOutlinedIcon from '../../components/icons/DeleteOutlined'
import EditOutlinedIcon from '../../components/icons/EditOutlined'
import { useVersionContext } from '../../hooks/useVersion'

export default function FieldLibrary() {
  const [param, setParam] = useState({
    pageNo: 1,
    pageSize: 10,
    search_CONTAINS_fieldName: '',
    search_EQ_createUserId: undefined,
  })
  const [openFieldLibrary, setOpenFieldLibrary] = useState(false)
  const [openField, setOpenField] = useState(false)
  const [openUploadLibrary, setOpenUploadLibrary] = useState(false)
  const [showDelLibrary, setShowDelLibrary] = useState(false)
  const [showDelField, setShowDelField] = useState(false)

  const [activeLibrary, setActiveLibrary] = useState<RuleLibrary>()
  const [activeField, setActiveField] = useState<string[]>([])
  const [activeEditLibrary, setActiveEditLibrary] = useState<RuleLibrary>()
  const [showLibrary, setShowLibrary] = useState(true) //是否展示左侧字段库列表
  const [messageApi, contextHolder] = message.useMessage()
  const [library, setLibrary] = useState<RuleLibrary[]>([])
  const { version } = useVersionContext()

  function xiChengText() {
    return version === 'xicheng' ? '!text-[22px]' : ''
  }

  const { refetch } = useQuery(
    ['getLibraryAll'],
    () =>
      extractResponse(() =>
        libraryTreeVo({ libraryName: '', libraryLevel: 0 })
      )(),
    {
      onSuccess(res) {
        setLibrary(res)
      },
    }
  )

  //fieldDelByIds

  const { data: fieldList, refetch: fieldListRefetch } = useQuery(
    ['getFieldPage', activeLibrary, param.pageNo],
    () => {
      return activeLibrary
        ? extractResponse(() =>
            getFieldPage(param.pageNo, param.pageSize, {
              ...param,
              search_EQ_libraryId: activeLibrary?.id,
            })
          )()
        : undefined
    }
  )

  const paginationObj = {
    total: fieldList?.total,
    showTotal: (total: any, range: any) =>
      `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
    defaultCurrent: 1,
    defaultPageSize: 10,
    onChange: (page: number) => {
      setParam(p => ({ ...p, pageNo: page }))
    },
    pageSizeOptions: ['5', '10', '50'],
    onShowSizeChange: (current: any, size: number) => {
      setParam(p => ({ ...p, pageSize: size }))
    },
    current: fieldList?.pageNo,
    className: xiChengText(),
  }

  const libraryTreeVoMutation = useMutation(libraryTreeVo, {
    onSuccess(res) {
      setLibrary(res.data.data)
    },
  })

  const inputSearch = debounce((str: string) => {
    libraryTreeVoMutation.mutate({ libraryName: str, libraryLevel: 0 })
  }, 500)

  useEffect(() => {
    if (activeEditLibrary) {
    }
  }, [activeEditLibrary])

  useEffect(() => {
    if (activeEditLibrary) {
      setOpenFieldLibrary(true)
    }
  }, [activeEditLibrary])

  const libraryDelByIdMutation = useMutation(libraryDelById, {
    onSuccess(res) {
      messageApi.open({ type: 'success', content: res.data.message })
      refetch()
      setShowDelLibrary(false)
      setActiveLibrary(undefined)
    },
  })

  const fieldDelByIdsMutation = useMutation(fieldDelByIds, {
    onSuccess(res) {
      messageApi.open({ type: 'success', content: res.data.message })
      fieldListRefetch()
      setShowDelField(false)
    },
  })

  const columns: ColumnsType<FieldType> = [
    {
      title: '字段名称',
      render: (item: FieldType) => <div>{item.fieldName}</div>,
      key: 'name',
      className: xiChengText(),
    },
    {
      title: '字段描述',
      render: (item: FieldType) => <div>{item.fieldDescribe}</div>,
      key: 'name',
      className: xiChengText(),
    },
    {
      title: '创建人',
      render: (item: FieldType) => <div>{item.createUserName}</div>,
      key: 'name',
      className: xiChengText(),
    },
    {
      title: '创建时间',
      render: (item: FieldType) => <div>{item.createTime}</div>,
      key: 'name',
      className: xiChengText(),
    },
  ]

  function delLibrary() {
    if (activeLibrary) {
      libraryDelByIdMutation.mutate(activeLibrary.id)
    }
  }

  function delFields() {
    fieldDelByIdsMutation.mutate(activeField)
  }

  return (
    <div className="px-4">
      {contextHolder}
      <div className="flex items-center  justify-between h-16">
        <div className={classNames('text-base', xiChengText())}>字段库表</div>
        {activeLibrary ? (
          <div className="flex">
            <Input
              placeholder="字段名称"
              className="!mr-4 !w-[200px]"
              value={param.search_CONTAINS_fieldName}
              onChange={e => {
                setParam(p => ({
                  ...p,
                  search_CONTAINS_fieldName: e.target.value,
                }))
              }}
              maxLength={40}
            />
            <UserSelect
              className="!mr-4 !w-[200px]"
              value={param.search_EQ_createUserId}
              onChange={e => {
                setParam(p => ({ ...p, search_EQ_createUserId: e }))
              }}
            />
            {/* <DatePicker
              className="!mr-4 !w-[200px]"
              onChange={e => {
                if (e) {
                  setParam(p => ({
                    ...p,
                    search_EQ_createTime:
                      e?.year() + '-' + (e?.month() + 1) + '-' + e?.date(),
                  }))
                }
              }}
            /> */}

            <Button
              onClick={() => {
                console.log(param)
                setParam(p => ({ ...p, pageNo: 1 }))
                fieldListRefetch()
              }}
              type="primary"
            >
              搜索
            </Button>
          </div>
        ) : null}
      </div>
      <div className="flex">
        {showLibrary ? (
          <div className="mt-2 w-60 h-[694px] border p-3">
            <Input
              placeholder="请输入"
              // value={libraryText}
              onChange={e => inputSearch(e.target.value)}
              className="w-[216px] h-8"
              maxLength={40}
              suffix={
                <div>
                  <PlusOutlined
                    className="w-3 h-3 mr-3 cursor-pointer"
                    onClick={() => {
                      setOpenFieldLibrary(true)
                    }}
                  />
                  <SyncOutlined
                    className="w-3 h-3 mr-3 cursor-pointer"
                    onClick={() => {
                      refetch()
                    }}
                  />
                  <MenuFoldOutlined
                    className="w-3 h-3 cursor-pointer"
                    onClick={() => {
                      setShowLibrary(false)
                    }}
                  />
                </div>
              }
            />
            <div className="mt-4 overflow-auto">
              {library.map(library => (
                <div
                  key={library.id}
                  className={classNames(
                    'mb-2 cursor-pointer min-h-6 leading-6 pl-1 flex items-center',
                    activeLibrary?.id === library.id
                      ? ' bg-primary-default bg-opacity-10'
                      : null,
                    xiChengText()
                  )}
                  onClick={() => {
                    setActiveLibrary(library)
                  }}
                >
                  {library.title}
                  {activeLibrary?.id === library.id ? (
                    <>
                      <div
                        className="ml-auto mr-2"
                        onClick={() => {
                          setActiveEditLibrary(library)
                        }}
                      >
                        <EditOutlinedIcon />
                      </div>
                      <div
                        onClick={() => {
                          setShowDelLibrary(true)
                        }}
                      >
                        <DeleteOutlinedIcon className="mr-2" />
                      </div>
                    </>
                  ) : null}
                </div>
              ))}
            </div>
          </div>
        ) : null}
        <div className="w-full pl-4">
          <div className="flex mt-2">
            {!showLibrary ? (
              <Button
                icon={<MenuUnfoldOutlined />}
                onClick={() => {
                  setShowLibrary(true)
                }}
              >
                展开数据结构
              </Button>
            ) : null}
            {activeLibrary ? (
              <>
                <Button
                  className="mr-4 ml-auto"
                  onClick={() => {
                    setOpenField(true)
                  }}
                >
                  新建字段
                </Button>
                <Button
                  onClick={() => {
                    if (activeField.length > 0) {
                      setShowDelField(true)
                    }
                  }}
                  className="mr-4"
                >
                  删除
                </Button>
                <Button
                  className="mr-4"
                  onClick={() => {
                    setOpenUploadLibrary(true)
                  }}
                >
                  上传
                </Button>
                <Button
                  href={
                    '/api/exactness/public/field/download?id=' +
                    activeLibrary?.id
                  }
                  className="mr-4"
                >
                  下载
                </Button>
              </>
            ) : null}
          </div>

          <Table
            rowSelection={{
              type: 'checkbox',
              onChange: item => {
                console.log(item)
                setActiveField(item as string[])
              },
            }}
            columns={columns}
            dataSource={fieldList?.list}
            className="mt-4"
            rowKey={item => item.id}
            pagination={paginationObj}
          />
        </div>
      </div>
      <LibraryAdd
        open={openFieldLibrary}
        close={() => {
          setOpenFieldLibrary(false)
          setActiveEditLibrary(undefined)
        }}
        refetch={refetch}
        data={activeEditLibrary}
      />
      <FieldAdd
        open={openField}
        close={setOpenField}
        activeLibrary={activeLibrary!}
        refetch={fieldListRefetch}
      />
      <UploadLibrary
        open={openUploadLibrary}
        close={() => {
          setOpenUploadLibrary(false)
          fieldListRefetch()
        }}
        libraryId={activeLibrary?.id!}
      />
      <Modal
        title="删除字段库表"
        open={showDelLibrary}
        onCancel={() => setShowDelLibrary(false)}
        onOk={delLibrary}
      >
        <div>是否删除该字段库表</div>
      </Modal>
      <Modal
        title="删除字段"
        open={showDelField}
        onCancel={() => setShowDelField(false)}
        onOk={delFields}
      >
        <div>是否删除该字段</div>
      </Modal>
    </div>
  )
}
