import React from 'react'
import { SvgProps } from './BaseSvg'

export default function WayLineIcon(props: SvgProps) {
  const Svg = () => (
    <svg
      width="14"
      height="16"
      viewBox="0 0 14 16"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path d="M11.867 11c-.934 0-1.8.6-2.067 1.467H3.733A1.935 1.935 0 0 1 1.8 10.533c0-1.066.867-1.866 1.933-1.866h6.534c1.8 0 3.266-1.467 3.266-3.267 0-1.8-1.466-3.267-3.266-3.267H4.2C3.933 1.267 3.133.667 2.133.667 1 .667 0 1.667 0 2.8s1 2.133 2.133 2.133c.934 0 1.8-.6 2.067-1.466h6c1.067 0 1.933.866 1.933 1.933 0 1.067-.8 1.933-1.866 1.933H3.733A3.272 3.272 0 0 0 .467 10.6c0 1.8 1.466 3.267 3.266 3.267h6c.267.866 1.067 1.466 2.067 1.466 1.2 0 2.133-1 2.133-2.133S13 11 11.867 11zM1.333 2.8c0-.467.4-.8.8-.8s.8.4.8.8-.333.867-.8.867c-.4 0-.8-.4-.8-.867zM11.867 14a.81.81 0 0 1-.8-.8c0-.4.4-.8.8-.8s.8.4.8.8-.4.8-.8.8z" />
    </svg>
  )

  return <Svg />
}
