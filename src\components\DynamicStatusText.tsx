import React, { useState, useEffect } from 'react'
import { ProcessStatus } from '../constants/project'

interface DynamicStatusTextProps {
  status: number
  baseText: string
  className?: string
  animationType?: 'dots' | 'pulse' | 'fade'
}

const DynamicStatusText: React.FC<DynamicStatusTextProps> = ({
  status,
  baseText,
  className = '',
  animationType = 'dots'
}) => {
  const [dots, setDots] = useState('')

  useEffect(() => {
    // 只有当状态为"进行中"时才显示动态效果
    if (status === ProcessStatus.PROCESSING && animationType === 'dots') {
      const interval = setInterval(() => {
        setDots(prev => {
          if (prev === '') return '.'
          if (prev === '.') return '..'
          if (prev === '..') return '...'
          return ''
        })
      }, 600) // 每600ms切换一次

      return () => clearInterval(interval)
    } else {
      // 其他状态不显示动态效果
      setDots('')
    }
  }, [status, animationType])

  // CSS动画样式
  const getAnimationStyle = () => {
    if (status !== ProcessStatus.PROCESSING) return {}

    switch (animationType) {
      case 'pulse':
        return {
          animation: 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite'
        }
      case 'fade':
        return {
          animation: 'fade 1.5s ease-in-out infinite'
        }
      default:
        return {}
    }
  }

  return (
    <span className={className} style={getAnimationStyle()}>
      {baseText}
      {status === ProcessStatus.PROCESSING && animationType === 'dots' && (
        <span
          className="inline-block w-6 text-left transition-opacity duration-200"
          style={{
            color: 'inherit',
            opacity: dots ? 1 : 0.7
          }}
        >
          {dots}
        </span>
      )}

      {/* CSS动画样式 */}
      <style jsx>{`
        @keyframes pulse {
          0%, 100% {
            opacity: 1;
          }
          50% {
            opacity: 0.7;
          }
        }

        @keyframes fade {
          0%, 100% {
            opacity: 1;
          }
          50% {
            opacity: 0.5;
          }
        }
      `}</style>
    </span>
  )
}

export default DynamicStatusText
