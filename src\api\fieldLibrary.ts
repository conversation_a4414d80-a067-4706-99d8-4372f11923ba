import request from './requestExactness'
import { PageData, Result } from './types'

export function librarySave(formData: { libraryName: string }) {
  return request.post<Result<string>>('/public/library/save', formData)
}

// 规则模板
export function libraryRuleSave(formData: {
  libraryName: string
  librarytype: string
  libraryParentId: string
}) {
  return request.post<Result<string>>('/public/library/save', formData)
}

export function getLibraryAll() {
  return request.get<Result<FieldLibraryType[]>>('/public/library/getAll')
}

// library/treeVo

export function libraryTreeVo(formData: {
  libraryName: string
  libraryLevel: number
}) {
  return request.post<Result<RuleLibrary[]>>('/public/library/treeVo', formData)
}

export function libraryDelById(id: string) {
  return request.get<Result<FieldLibraryType[]>>(
    '/public/library/delById?id=' + id
  )
}
// 字段库下载
export function libraryDownload(libraryId: string) {
  return request.get<Result<FieldLibraryType[]>>(
    '/public/field/download?id=' + libraryId
  )
}
// 字段库模板下载
export function libraryDownloadTemplate(libraryId: string) {
  return request.get<Result<FieldLibraryType[]>>(
    '/public/field/downloadTemplate?id=' + libraryId
  )
}
// 字段库上传
export function libraryUpload(param: FormData) {
  return request.post<Result<string>>('/public/field/upload', param, {
    headers: { 'Content-Type': 'multipart/form-data' },
  })
}

export function fieldSave(formData: {
  fieldName: string
  fieldDescribe: string
}) {
  return request.post<Result<string>>('/public/field/save', formData)
}

export function getFieldPage(
  pageNo: number,
  pageSize: number,
  params: Record<string, any>
) {
  const param = JSON.parse(JSON.stringify(params))
  for (const item in param) {
    if (param[item] === '') {
      delete params[item]
    }
  }
  delete params.pageNo
  delete params.pageSize

  return request.post<Result<PageData<FieldType>>>('/public/field/page', {
    pageNo,
    pageSize,
    param: params,
  })
}

export function fieldDelByIds(formData: string[]) {
  return request.post<Result<string>>('/public/field/delByIds', formData)
}

export function getLibraryById(id: string) {
  return request.get<Result<FieldLibraryType[]>>(
    '/public/library/getById?id=' + id
  )
}

export function getRuleByParentId(libraryParentId: string) {
  return request.get<Result<RuleMouldConfig[]>>(
    '/public/library/getByParentId?libraryParentId=' + libraryParentId
  )
}
