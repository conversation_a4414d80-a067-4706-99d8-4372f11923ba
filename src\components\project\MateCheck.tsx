import React, { useEffect, useState } from 'react'
import {
  ConfigProvider,
  Form,
  Modal,
  Pagination,
  Select,
  message,
  theme,
} from 'antd'
import { UseMutationResult, useQuery } from 'react-query'
import Table, { ColumnsType } from 'antd/lib/table'
import { AxiosResponse } from 'axios'

import { getHeards } from '../../api/project'
import { extractResponse } from '../../api/util'
import { getFieldPage } from '../../api/fieldLibrary'
import { PageData, Result } from '../../api/types'
import { mateCheckVerification } from '../../utils/verification'

type MateCheckType = {
  open: boolean
  close: React.Dispatch<React.SetStateAction<boolean>>
  activeConfig?: CatalogueFile
  projectConfigSaveMutation: UseMutationResult<
    AxiosResponse<Result<PageData<ProjectType>>, any>,
    unknown,
    CatalogueFile,
    unknown
  >
  disabled: boolean
  libraryId: string
}

export default function MateCheck({
  open,
  close,
  activeConfig,
  projectConfigSaveMutation,
  disabled,
  libraryId,
}: MateCheckType) {
  const form = Form.useForm()[0]
  const [sheetFieldList, setSheetFieldList] = useState<
    { label: string; value: string }[]
  >([])
  // 所有字段
  const [allFieldList, setAllFieldList] = useState<FieldType[]>([])

  // excel可选字段
  const [excelFieldList, setExcelFieldList] = useState<FieldType[]>([])
  const [excelAndFieldMap, setExcelAndFieldMap] = useState<
    Record<string, string>
  >({})
  const [current, setCurrent] = useState(1)
  const [heardData, setHeardData] = useState<string[]>([])
  // 档号等可选字段
  const { data } = useQuery(
    ['getFieldPage', libraryId, open],
    extractResponse(() =>
      getFieldPage(1, 99999999, {
        search_EQ_libraryId: libraryId,
      })
    ),
    {
      onSuccess(res) {
        setExcelFieldList(res?.list)
        setAllFieldList(res?.list)
      },
      enabled: !!libraryId && open,
    }
  )

  useQuery(
    ['getHeards', activeConfig, open],
    activeConfig
      ? extractResponse(() =>
          getHeards({
            path: activeConfig?.filePath,
            sheetName: activeConfig?.sheetName,
          })
        )
      : () => undefined,
    {
      onSuccess(res) {
        if (res) {
          const list = res.reduce((map, item) => {
            const newStr = item.replace(/\s/g, '')
            map.push({ label: newStr, value: newStr })
            return map
          }, [] as { label: string; value: string }[])
          setSheetFieldList(list)
          const newArr = res.map(item => item.replace(/\s/g, ''))
          console.log(newArr)
          setHeardData(newArr)
        }
      },
      enabled: !!activeConfig?.sheetName && open,
    }
  )

  useEffect(() => {
    if (open) {
      if (activeConfig) {
        if (activeConfig.ruleMapping) {
          const list: Record<string, string>[] = JSON.parse(
            activeConfig.ruleMapping
          )
          const map = list.reduce((map, item) => {
            map[item.key] = item.value
            updateLibrary(item.value, item.key)
            return map
          }, {} as Record<string, string>)
          setExcelAndFieldMap(map)
        } else {
          if (heardData) {
            const map = heardData?.reduce((map, item) => {
              const sameField = allFieldList.find(
                field => field.fieldName === item
              )
              if (sameField) {
                map[item] = sameField.id
              } else {
                map[item] = undefined
              }
              return map
            }, {} as Record<string, any>)
            initFieldLibrary(map)
            setExcelAndFieldMap(map)
          }
        }
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeConfig, allFieldList, heardData, open])

  useEffect(() => {
    if (!open) {
      setAllFieldList([])
      setSheetFieldList([])
      setExcelFieldList([])
      setExcelAndFieldMap({})
      setCurrent(1)
    }
  }, [open])

  function initFieldLibrary(fieldMap: Record<string, any>) {
    const valueList = Object.values(fieldMap)
    console.log({ valueList })
    const list = allFieldList.reduce((map, item) => {
      const sameField = valueList.find(value => value === item.id)
      if (!sameField) {
        map.push(item)
      }
      return map
    }, [] as FieldType[])
    console.log({ list })
    setExcelFieldList([...list])
  }

  function updateLibrary(e: string, item: string) {
    const excelFieldMap = { ...excelAndFieldMap, [item]: e }
    const list = allFieldList.reduce((map, field) => {
      const arr = Object.values(excelFieldMap)
      if (!arr.find(item => item === field.id)) {
        map.push(field)
      }
      return map
    }, [] as FieldType[])
    console.log(list)
    setExcelFieldList([...list])
    setExcelAndFieldMap(p => ({ ...p, [item]: e }))
  }

  const columns: ColumnsType<{ label: string; value: string }> = [
    {
      title: '列名',
      render: (item, _, index) => index + 1,
      key: 'index',
    },
    {
      title: 'excel',
      render: item => <div className="truncate">{item.label}</div>,
      key: 'label',
    },
    {
      title: '字段',
      className: 'w-[200px]',
      render: item => {
        const field = data?.list.find(
          datas => datas.id === excelAndFieldMap[item.value]
        )
        let list = excelFieldList
        if (field) {
          list = [field, ...excelFieldList]
        }
        return (
          <Select
            allowClear
            placeholder="请选择"
            className="w-[200px]"
            disabled={disabled}
            options={list}
            fieldNames={{ label: 'fieldName', value: 'id' }}
            value={excelAndFieldMap[item.value]}
            onChange={(e: string) => {
              updateLibrary(e, item.value)
            }}
          />
        )
      },
      key: 'value',
    },
  ]

  const paginationObj = {
    total: heardData?.length,
    pageSize: 5,
    current: current,
    onChange: (page: number) => {
      setCurrent(page)
    },
  }

  function submit() {
    const arr = []
    const error = mateCheckVerification(excelAndFieldMap)
    console.log(error)
    if (error?.error) {
      message.warning(error.label)
      return
    }
    for (const key in excelAndFieldMap) {
      const field = allFieldList.find(
        field => field.id === excelAndFieldMap[key]
      )
      arr.push({
        key: key,
        value: excelAndFieldMap[key],
        label: field?.fieldName,
      })
    }

    if (activeConfig) {
      projectConfigSaveMutation.mutate({
        ...activeConfig,
        ruleMapping: JSON.stringify(arr),
      })
    }
    close(false)
  }
  return (
    <Modal
      title="匹配检查"
      open={open}
      onCancel={() => close(false)}
      onOk={() => {
        submit()
      }}
      className="w-[608px]"
    >
      <Form
        form={form}
        // onFinish={submit}
        // style={{ width: '380px' }}
      >
        <div className="flex ">
          {/* <FormItem
            label="档号字段"
            rules={[{ required: true, message: '请输入字段库名称' }]}
          >
            <Select
              placeholder="请选择"
              allowClear
              value={fieldMap['fieldName']}
              className="w-[358px]"
              disabled={disabled}
              options={
                allFieldList.find(datas => datas.id === fieldMap['fieldName'])
                  ? [
                      allFieldList.find(
                        datas => datas.id === fieldMap['fieldName']
                      )!,
                      ...fieldList,
                    ]
                  : [...fieldList]
              }
              onChange={e => {
                updateFieldLibrary(e, 'fieldName')
              }}
              fieldNames={{ label: 'fieldName', value: 'id' }}
            />
          </FormItem> */}
          {/* <FormItem
            label=""
            rules={[{ required: true, message: '请输入字段库名称' }]}
            // name="isHavePiece"
            valuePropName="checked"
          >
            <Checkbox
              checked={!!fieldMap['isHavePiece']}
              className="ml-2"
              disabled={disabled}
              onChange={e => {
                console.log(e.target.value)
                setFieldMap(p => ({
                  ...p,
                  isHavePiece: e.target.checked ? 1 : 0,
                }))
              }}
            >
              是否包含件号
            </Checkbox>
          </FormItem> */}
        </div>
        {/* <FormItem
          label="页号字段"
          rules={[{ required: true, message: '请输入字段库名称' }]}
        >
          <Select
            placeholder="请选择"
            allowClear
            value={fieldMap['pageNum']}
            disabled={disabled}
            options={
              data?.list.find(datas => datas.id === fieldMap['pageNum'])
                ? [
                    data?.list.find(
                      datas => datas.id === fieldMap['pageNum']
                    ) as FieldType,
                    ...fieldList,
                  ]
                : [...fieldList]
            }
            onChange={e => {
              updateFieldLibrary(e, 'pageNum')
            }}
            fieldNames={{ label: 'fieldName', value: 'id' }}
          />
        </FormItem>
        <FormItem
          label="件号字段"
          rules={[{ required: true, message: '请输入字段库名称' }]}
        >
          <Select
            placeholder="请选择"
            disabled={disabled}
            allowClear
            value={fieldMap['pieceField']}
            options={
              data?.list.find(datas => datas.id === fieldMap['pieceField'])
                ? [
                    data?.list.find(
                      datas => datas.id === fieldMap['pieceField']
                    ) as FieldType,
                    ...fieldList,
                  ]
                : [...fieldList]
            }
            onChange={e => {
              updateFieldLibrary(e, 'pieceField')
            }}
            fieldNames={{ label: 'fieldName', value: 'id' }}
          />
        </FormItem> */}
        <Table
          dataSource={sheetFieldList}
          columns={columns}
          pagination={paginationObj}
          className="mt-2 "
          rowKey={record => record.label}
        />
      </Form>
    </Modal>
  )
}
