export function getLocalStorage(name: string) {
  return window.localStorage.getItem(name)
}

export function setLocalStorage(name: string, value: string) {
  return window.localStorage.setItem(name, value)
}

export function getSearch(str: string, val: string) {
  if (str.indexOf(val) === -1) {
    return ''
  }
  const list = str.split('?')[1].split('&')
  console.log(list)
  const arr = list.find(item => item.split('=')[0] === val)
  if (arr) {
    return arr.split('=')[1]
  } else {
    return ''
  }
}

export function max(a: number, b: number) {
  return a > b ? a : b
}
