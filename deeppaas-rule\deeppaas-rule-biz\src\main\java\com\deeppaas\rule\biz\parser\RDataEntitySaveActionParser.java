package com.deeppaas.rule.biz.parser;

import com.deeppaas.rule.biz.action.RAction;
import com.deeppaas.rule.biz.action.RDataEntitySaveAction;
import com.fasterxml.jackson.databind.JsonNode;

/**
 * 数据模型保存动作JSON解析器
 * <AUTHOR>
 * @date 2022/6/14
 */
public class RDataEntitySaveActionParser extends RActionParser{
    private static final String KEY_DATA_ENTITY = "dataEntity";

    public static RAction buildAction(JsonNode actionNode) {
        RDataEntitySaveAction action = new RDataEntitySaveAction();
        buildBaseInfo(action, actionNode);
        action.setDataEntity(actionNode.get(KEY_DATA_ENTITY).textValue());
        action.setDataBind(RDataBindParser.buildDataBind(actionNode.get(KEY_DATA_BIND)));
        return action;
    }
}
