import { Modal, Input, Form, Select } from 'antd'
import FormItem from 'antd/es/form/FormItem'

import React, { useEffect, useState } from 'react'
import { useMutation } from 'react-query'
import { useNavigate } from 'react-router-dom'
import { extractResponse } from '../../api/util'

import { taskInfoSave } from '../../api/project'
import { libraryTreeVo } from '../../api/fieldLibrary'

type ProjectCreateType = {
  open: boolean
  close: () => void
}
const reg = /^(?:\d|[1-9]\d|100)(?:\.\d{1,2})?$/
export default function ProjectCreate({
  open,
  close,
}: // activeLibrary,
// refetch,
ProjectCreateType) {
  const form = Form.useForm()[0]
  const [library, setLibrary] = useState<RuleLibrary[]>([])
  const navigate = useNavigate()

  useEffect(() => {
    if (open) {
      form.setFieldsValue({
        taskName: '',
        libraryId: '',
        exactnessScale: '100',
        manualRatio: '0',
      })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open])

  // fieldSave
  const taskConfigSaveMutation = useMutation(extractResponse(taskInfoSave), {
    onSuccess(res) {
      console.log(res.id)
      close()
      navigate(
        '/projectManage/ProjectAdd?id=' + res.id + '&libraryId=' + res.libraryId
      )
    },
  })

  const libraryTreeVoMutation = useMutation(extractResponse(libraryTreeVo), {
    onSuccess(res) {
      console.log(res)
      setLibrary(res)
    },
  })

  useEffect(() => {
    libraryTreeVoMutation.mutate({ libraryLevel: 1, libraryName: '' })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  function submit(values: any) {
    console.log(values)
    taskConfigSaveMutation.mutate({ ...values })
  }

  return (
    <Modal
      title="新建任务"
      open={open}
      onCancel={() => {
        close()
        form.resetFields()
      }}
      onOk={() => {
        form.submit()
      }}
    >
      <Form
        form={form}
        onFinish={submit}
        labelCol={{ span: 10 }}
        style={{ width: '380px' }}
      >
        <FormItem
          label="任务名称"
          rules={[{ required: true, message: '请输入任务名称' }]}
          name="taskName"
        >
          <Input maxLength={40} />
        </FormItem>
        <FormItem
          label="字段库表"
          name="libraryId"
          rules={[{ required: true, message: '请选择字段库表' }]}
        >
          <Select
            options={library}
            fieldNames={{ label: 'title', value: 'id' }}
          />
        </FormItem>
        <FormItem
          label="人工抽查"
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || reg.test(value)) {
                  return Promise.resolve()
                }
                return Promise.reject(new Error('输入范围为0-100'))
              },
            }),
          ]}
          name="manualRatio"
        >
          <Input
            maxLength={40}
            suffix={'%'}
            value={form.getFieldValue('manualRatio')}
            onChange={e => {
              if (reg.test(e.target.value)) {
                form.setFieldValue('manualRatio', e.target.value)
              }
            }}
          />
        </FormItem>
        <FormItem
          label="软件抽查"
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || reg.test(value)) {
                  return Promise.resolve()
                }
                return Promise.reject(new Error('输入范围为0-100'))
              },
            }),
          ]}
          name="exactnessScale"
        >
          <Input
            maxLength={40}
            suffix={'%'}
            value={form.getFieldValue('exactnessScale')}
            onChange={e => {
              if (reg.test(e.target.value)) {
                form.setFieldValue('exactnessScale', e.target.value)
              }
            }}
          />
        </FormItem>
      </Form>
    </Modal>
  )
}
