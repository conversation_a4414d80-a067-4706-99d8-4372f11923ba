declare interface userType {
  id: string
  name: string
  code: string
  aidosRole: string
  usable: number
}

declare interface UserParam {
  name: string
  code: string
  aidosRole: string
  usable: number
}

declare interface EventType {
  id: string
  name: string
}

declare interface ServiceType {
  id: string | number
  name: string
  userId?: string
  templates: string
}

declare interface ClassificationType {
  id: string | number
  name: string
  template: MouldImageType[]
}

declare interface ClassificationImageType {
  id: string
  imageTemplateId: string
  imageTemplateTypeId: string
  processId: string
  processTaskId: string
}

declare interface MouldImageType {
  id: string
  name: string
  path: string
  initials: string
  label?: string
}

export type userRole = 'admin' | 'user' | 'superAdmin'
