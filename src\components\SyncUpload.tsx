import React from 'react'
import { Upload, UploadProps, message } from 'antd'

interface SyncUploadProps {
  antProp: UploadProps
}

export default function SyncUpload(
  props: React.PropsWithChildren<SyncUploadProps>
) {
  const { antProp } = props

  const handleChange = (info: any) => {
    let { fileList } = info
    fileList = fileList.map((file: any) => {
      if (file.response) {
        file.url = file.response.url
      }
      return file
    })
    if (antProp.onChange) {
      antProp.onChange(fileList)
    }
    const { status, name } = info.file
    if (status === 'error') {
      message.error(`${name} 上传失败.`)
    }
  }

  return (
    <Upload {...antProp} onChange={handleChange}>
      {props.children}
    </Upload>
  )
}
