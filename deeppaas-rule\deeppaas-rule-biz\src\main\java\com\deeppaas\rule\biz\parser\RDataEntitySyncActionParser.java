package com.deeppaas.rule.biz.parser;

import com.deeppaas.common.exception.RunException;
import com.deeppaas.rule.biz.action.RAction;
import com.deeppaas.rule.biz.action.RDataEntitySyncAction;
import com.deeppaas.rule.biz.databind.RDataBind;
import com.deeppaas.rule.biz.model.RConditionModel;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/28
 */
public class RDataEntitySyncActionParser extends RActionParser{
    private static final String KEY_SOURCE_ENTITY = "sourceEntity";
    private static final String KEY_TARGET_ENTITY = "targetEntity";
    private static final String KEY_SOURCE_CONDITION = "sourceCondition";
    private static final String KEY_TARGET_CONDITION = "targetCondition";
    private static final String KEY_MATCH_FIELDS = "matchFields";
    private static final String KEY_UPDATE_FIELDS = "updateFields";

    private static final String KEY_SOURCE_FIELD = "sourceField";
    private static final String KEY_TARGET_FIELD = "targetField";
    private static final String KEY_DATA_BIND = "dataBind";

    protected static RAction buildAction(JsonNode actionNode) {
        RDataEntitySyncAction action = new RDataEntitySyncAction();
        buildBaseInfo(action, actionNode);
        action.setSourceEntity(actionNode.get(KEY_SOURCE_ENTITY).textValue());
        action.setTargetEntity(actionNode.get(KEY_TARGET_ENTITY).textValue());
        JsonNode sourceConditionNode = actionNode.get(KEY_SOURCE_CONDITION);
        RConditionModel sourceConditionModel = RConditionModelParser.buildRConditionModel(sourceConditionNode);
        action.setSourceCondition(sourceConditionModel);
        JsonNode targetConditionNode = actionNode.get(KEY_TARGET_CONDITION);
        RConditionModel targetConditionModel = RConditionModelParser.buildRConditionModel(targetConditionNode);
        action.setTargetCondition(targetConditionModel);
        JsonNode matchFieldsNode = actionNode.get(KEY_MATCH_FIELDS);
        List<RDataEntitySyncAction.MatchField> matchFields = buildMatchFields(matchFieldsNode);
        action.setMatchFields(matchFields.toArray(new RDataEntitySyncAction.MatchField[matchFields.size()]));
        JsonNode updateNode = actionNode.get(KEY_UPDATE_FIELDS);
        List<RDataEntitySyncAction.SyncField> updateFieldList = buildSyncFields(updateNode);
        action.setUpdateFields(updateFieldList.toArray(new RDataEntitySyncAction.SyncField[updateFieldList.size()]));
        return action;
    }

    private static List<RDataEntitySyncAction.MatchField> buildMatchFields(JsonNode matchFieldsNode) {
        if (matchFieldsNode.isArray()) {
            List<RDataEntitySyncAction.MatchField> matchFields = new ArrayList<>();
            Iterator<JsonNode> matchFieldIterator = matchFieldsNode.elements();
            while (matchFieldIterator.hasNext()) {
                JsonNode matchFieldNode = matchFieldIterator.next();
                String sourceField = matchFieldNode.get(KEY_SOURCE_FIELD).textValue();
                String targetField = matchFieldNode.get(KEY_TARGET_FIELD).textValue();
                RDataEntitySyncAction.MatchField matchField = new RDataEntitySyncAction.MatchField();
                matchField.setSourceField(sourceField);
                matchField.setTargetField(targetField);
                matchFields.add(matchField);
            }
            return matchFields;
        } else {
            throw RunException.error("更新字段解析异常");
        }
    }

    private static List<RDataEntitySyncAction.SyncField> buildSyncFields(JsonNode updateNode) {
        if (updateNode.isArray()) {
            List<RDataEntitySyncAction.SyncField> updateFieldList = new ArrayList<>();
            Iterator<JsonNode> updateFieldIterator = updateNode.elements();
            while (updateFieldIterator.hasNext()) {
                RDataEntitySyncAction.SyncField syncField = new RDataEntitySyncAction.SyncField();
                JsonNode updateFieldNode = updateFieldIterator.next();
                String targetField = updateFieldNode.get(KEY_TARGET_FIELD).textValue();
                syncField.setTargetField(targetField);
                JsonNode dataBindNode = updateNode.get(KEY_DATA_BIND);
                if(dataBindNode!=null){
                    RDataBind dataBind = RDataBindParser.buildDataBind(dataBindNode);
                    syncField.setDataBind(dataBind);
                }
                JsonNode sourceFieldNode = updateFieldNode.get(KEY_SOURCE_FIELD);
                if(sourceFieldNode!=null){
                    syncField.setSourceField(sourceFieldNode.textValue());
                }
                updateFieldList.add(syncField);
            }
            return updateFieldList;
        } else {
            throw RunException.error("更新字段解析异常");
        }
    }
}
