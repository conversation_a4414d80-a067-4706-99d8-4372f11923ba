package com.deeppaas.rule.biz.parser;

import com.deeppaas.rule.biz.action.RDataEntityGetAction;
import com.deeppaas.rule.biz.action.RDataEntityListAction;
import com.deeppaas.rule.biz.action.RDataEntityPageAction;
import com.deeppaas.rule.biz.databind.RDataBind;
import com.deeppaas.rule.biz.model.RConditionModel;
import com.deeppaas.rule.biz.model.RDataQuerySortOrder;
import com.deeppaas.rule.biz.model.RDataQueryPermit;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * 数据模型查询动作JSON解析器
 * <AUTHOR>
 * @date 2022/6/14
 */
public class RDataEntityQueryActionParser extends RActionParser{
    private static final String KEY_DATA_ENTITY = "dataEntity";
    private static final String KEY_PERMITS = "permits";
    private static final String KEY_DEFAULT_CONDITION = "defaultCondition";

    private static final String KEY_PERMIT_NAME = "permitName";
    private static final String KEY_PERMIT_AUTH_BIND = "authBind";
    private static final String KEY_PERMIT_CONDITION = "condition";
    private static final String KEY_PAGE_NO = "pageNoBind";
    private static final String KEY_PAGE_SIZE = "pageSizeBind";
    private static final String KEY_SORT_ORDERS = "sortOrders";
    private static final String KEY_ORDER_FIELD = "field";
    private static final String KEY_ORDER_TYPE = "type";

    protected static RDataEntityGetAction buildGetAction(JsonNode actionNode) {
        String entity = actionNode.get(KEY_DATA_ENTITY).textValue();
        RDataQueryPermit[] permits = buildPermits(actionNode.get(KEY_PERMITS));
        JsonNode defaultConditionNode = actionNode.get(KEY_DEFAULT_CONDITION);
        RConditionModel defaultConditionModel = RConditionModelParser.buildRConditionModel(defaultConditionNode);

        RDataEntityGetAction action = new RDataEntityGetAction(entity, permits, defaultConditionModel);
        buildBaseInfo(action, actionNode);
        return action;
    }

    protected static RDataEntityListAction buildListAction(JsonNode actionNode) {
        String entity = actionNode.get(KEY_DATA_ENTITY).textValue();
        RDataQueryPermit[] permits = buildPermits(actionNode.get(KEY_PERMITS));
        JsonNode defaultConditionNode = actionNode.get(KEY_DEFAULT_CONDITION);
        RConditionModel defaultConditionModel = RConditionModelParser.buildRConditionModel(defaultConditionNode);
        RDataBind pageNoBind = RDataBindParser.buildDataBind(actionNode.get(KEY_PAGE_NO));
        RDataBind pageSizeBind = RDataBindParser.buildDataBind(actionNode.get(KEY_PAGE_SIZE));
        RDataQuerySortOrder[] orderSorts = buildOrderSorts(actionNode.get(KEY_SORT_ORDERS));
        RDataEntityListAction action = new RDataEntityListAction(entity, permits, defaultConditionModel,
                pageNoBind, pageSizeBind, orderSorts);
        buildBaseInfo(action, actionNode);
        return action;
    }

    protected static RDataEntityPageAction buildPageAction(JsonNode actionNode) {
        String entity = actionNode.get(KEY_DATA_ENTITY).textValue();
        RDataQueryPermit[] permits = buildPermits(actionNode.get(KEY_PERMITS));
        JsonNode defaultConditionNode = actionNode.get(KEY_DEFAULT_CONDITION);
        RConditionModel defaultConditionModel = RConditionModelParser.buildRConditionModel(defaultConditionNode);
        RDataBind pageNoBind = RDataBindParser.buildDataBind(actionNode.get(KEY_PAGE_NO));
        RDataBind pageSizeBind = RDataBindParser.buildDataBind(actionNode.get(KEY_PAGE_SIZE));
        RDataQuerySortOrder[] orderSorts = buildOrderSorts(actionNode.get(KEY_SORT_ORDERS));
        RDataEntityPageAction action = new RDataEntityPageAction(entity, permits, defaultConditionModel,
                pageNoBind, pageSizeBind, orderSorts);
        buildBaseInfo(action, actionNode);
        return action;
    }

    private static RDataQueryPermit[] buildPermits(JsonNode permitsNode) {
        if(permitsNode==null){
            return null;
        }
        List<RDataQueryPermit> permits = new ArrayList<>();
        if (permitsNode.isArray()) {
            Iterator<JsonNode> permitNodes = permitsNode.elements();
            while (permitNodes.hasNext()) {
                JsonNode permitNode = permitNodes.next();
                RDataQueryPermit permit = new RDataQueryPermit();
                permit.setPermitName(permitNode.get(KEY_PERMIT_NAME).textValue());
                permit.setAuthBind(RDataBindParser.buildDataBind(permitNode.get(KEY_PERMIT_AUTH_BIND)));
                permit.setCondition(RConditionModelParser.buildRConditionModel(permitNode.get(KEY_PERMIT_CONDITION)));
                permits.add(permit);
            }
        }
        return permits.toArray(new RDataQueryPermit[permits.size()]);
    }

    private static RDataQuerySortOrder[] buildOrderSorts(JsonNode sortOrdersNode) {
        if(sortOrdersNode==null){
            return null;
        }
        List<RDataQuerySortOrder> orders = new ArrayList<>();
        if (sortOrdersNode.isArray()) {
            Iterator<JsonNode> orderNodes = sortOrdersNode.elements();
            while (orderNodes.hasNext()) {
                JsonNode orderNode = orderNodes.next();
                RDataQuerySortOrder order = new RDataQuerySortOrder();
                order.setField(orderNode.get(KEY_ORDER_FIELD).textValue());
                order.setType(orderNode.get(KEY_ORDER_TYPE).textValue());
                orders.add(order);
            }
        }
        return orders.toArray(new RDataQuerySortOrder[orders.size()]);
    }

}
