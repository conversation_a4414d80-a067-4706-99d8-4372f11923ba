import React from 'react'
import { SvgProps } from './BaseSvg'

export default function TaskListenIcon(props: SvgProps) {
  const Svg = () => (
    <svg
      width="15"
      height="14"
      viewBox="0 0 15 14"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path d="M11.203 0c.857 0 1.547.709 1.547 1.577v3.851a.5.5 0 0 1-1 0V1.577A.563.563 0 0 0 11.203 1H2.298a.563.563 0 0 0-.548.577v10.846c0 .322.248.577.547.577H6.75a.5.5 0 0 1 0 1H2.297C1.44 14 .75 13.291.75 12.423V1.577C.75.709 1.44 0 2.297 0h8.905zM14.5 10.625c.07 0 .128.06.125.13a3.19 3.19 0 0 1-5.669 1.872l-.317.246a.125.125 0 0 1-.201-.1l.009-1.49a.126.126 0 0 1 .155-.12l1.457.351c.102.025.13.156.047.22l-.36.28a2.181 2.181 0 0 0 1.692.798c.58 0 1.139-.23 1.548-.64.384-.385.61-.89.637-1.428a.124.124 0 0 1 .125-.119h.752zm-3.063-3.188c1.002 0 1.897.463 2.482 1.186l.317-.246a.125.125 0 0 1 .201.1l-.009 1.49a.126.126 0 0 1-.155.12l-1.457-.351a.125.125 0 0 1-.047-.22l.36-.28a2.18 2.18 0 0 0-1.692-.798c-.58 0-1.139.23-1.548.64-.384.385-.61.89-.637 1.428a.124.124 0 0 1-.125.119h-.752a.125.125 0 0 1-.125-.13 3.19 3.19 0 0 1 3.188-3.057zM5.25 7.5a.5.5 0 0 1 0 1h-1a.5.5 0 0 1 0-1h1zm4-4a.5.5 0 0 1 0 1h-5a.5.5 0 0 1 0-1h5z" />
    </svg>
  )

  return <Svg />
}
