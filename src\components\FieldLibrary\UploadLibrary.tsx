import { Modal, Input, Form, Upload, UploadProps, Button, message } from 'antd'
import FormItem from 'antd/es/form/FormItem'
import React, { useEffect, useState } from 'react'
import { libraryUpload } from '../../api/fieldLibrary'

type UploadLibraryType = {
  open: boolean
  close: () => void
  libraryId: string
}

export default function UploadLibrary({
  open,
  close,
  libraryId,
}: UploadLibraryType) {
  const form = Form.useForm()[0]
  const [file, setFile] = useState<File>()

  useEffect(() => {
    if (open) {
      form.setFieldValue('fileName', '')
      setFile(undefined)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open])

  const props: UploadProps = {
    name: 'file',
    headers: {
      authorization: 'authorization-text',
    },
    customRequest() {},
    itemRender() {},
    onChange(info) {
      if (info && info.file) {
        console.log(info.file)
        setFile(info.file.originFileObj)
        form.setFieldValue('fileName', info.file.name)
      }
    },
  }

  function submit() {
    if (!file) {
      return
    }
    const formData = new FormData()
    formData.append('file', file)
    formData.append('libraryId', libraryId)
    libraryUpload(formData).then(res => {
      message.success(res.data.message)
      close()
    })
  }

  return (
    <Modal
      title="上传"
      open={open}
      onCancel={() => {
        close()
      }}
      onOk={() => {
        form.submit()
      }}
    >
      <Form form={form} onFinish={submit}>
        <div className="flex justify-between items-center mt-8 ml-6">
          <FormItem
            label="选择文件"
            rules={[{ required: true, message: '请选择文件' }]}
            name="fileName"
          >
            <Input
              placeholder="请选择"
              className="!w-[294px]"
              disabled
              suffix={
                <Upload {...props}>
                  <div className="text-primary-default cursor-pointer">
                    选择
                  </div>
                </Upload>
              }
            />
          </FormItem>
          {/* <Button
            type="link"
            className=" text-primary-default  text-sm cursor-pointer pr-0 -mt-3"
            href={'/api/exactness/public/field/libraryDownloadTemplate'}
          >
            下载导入模板
          </Button> */}
        </div>
      </Form>
    </Modal>
  )
}
