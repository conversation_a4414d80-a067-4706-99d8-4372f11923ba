const createConfig = require('one-start/createConfig')
const release = require('./release')

const proxyConfig = {
  local: 'http://localhost:8201',
  // local: 'http://**************:8201', //du
  // local: 'http://*************:8201',
  // local: 'http://46c7w47384.zicp.vip',
  // local: 'http://*************:8001',
  // local: 'http://*************:8102/',
}

module.exports = createConfig({
  locale: 'zh-CN',
  devCommand: 'vite',
  buildCommand: 'tsc && vite build',
  buildDir: 'dist',
  beforeStart({ stage }) {
    if (stage === 'development') {
      process.env.DEV_PROXY = proxyConfig.local
    } else {
      process.env.NODE_ENV = 'production'
      process.env.BASE_URL = '/static/'
    }
  },
  afterBuild({ head, body, createLink, createScript }) {
    const headCode = [
      ...head.scripts.map(script => createScript(script)),
      ...head.links
        .filter(link => link.rel !== 'icon')
        .map(link => createLink(link)),
    ]
    const bodyCode = body.scripts.map(script => createScript(script))

    console.log('head\n')
    console.log(headCode.join('\n'))
    release(headCode.join('\n    '))

    console.log()

    console.log('body\n')
    console.log(bodyCode.join('\n'))
  },
  uploadCommand() {
    return 'echo "uploaded"'
  },
  stages: {
    development: {},
    testing: {},
    production: {},
  },
  options: [
    // {
    //   name: 'proxy',
    //   message: 'Choose Dev Proxy',
    //   choices: [
    //     { name: 'local', message: 'Local Server' },
    //     { name: 'daily', message: 'Daily Server' },
    //   ],
    //   stages: ['development'],
    // },
  ],
})
