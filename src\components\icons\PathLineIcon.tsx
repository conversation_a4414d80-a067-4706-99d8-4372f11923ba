import React from 'react'
import { SvgProps } from './BaseSvg'

export default function PathLine(props: SvgProps) {
  const Svg = () => (
    <svg
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="true"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path d="M9.5 5.75a3.75 3.75 0 0 0-3.708 3.188H4a.437.437 0 0 1-.438-.438V2.812H8.11A1.499 1.499 0 0 0 11 2.25a1.5 1.5 0 0 0-2.89-.563H.75v1.125h1.688V8.5c0 .863.7 1.563 1.562 1.563h1.792A3.75 3.75 0 1 0 9.5 5.75zm2 4.156a.125.125 0 0 1-.125.125h-1.344v1.344a.125.125 0 0 1-.125.125h-.812a.125.125 0 0 1-.125-.125v-1.344H7.625a.125.125 0 0 1-.125-.125v-.812c0-.069.056-.125.125-.125h1.344V7.625c0-.069.056-.125.125-.125h.812c.069 0 .125.056.125.125v1.344h1.344c.069 0 .125.056.125.125v.812z" />
    </svg>
  )

  return <Svg />
}
