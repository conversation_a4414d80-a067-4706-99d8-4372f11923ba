import { AxiosResponse } from 'axios'
import { PageData, PageInfo, Result, SuccessResult } from '../api/types'

export function extractData<T>(response: AxiosResponse<Result<T>>) {
  const { data } = response.data as SuccessResult<T>
  return data
}

export function extractResponse<T, P extends any[] = any[]>(
  requestFn: (...args: P) => Promise<AxiosResponse<Result<T>>>
) {
  return (...args: P) =>
    requestFn(...args).then(response => extractData(response))
}

export function splitPageData<T = any>(data?: PageData<T>): [T[], PageInfo] {
  if (!data) {
    return [[], { pageNo: 1, pageSize: 10, sort: '', total: 0, totalPages: 0 }]
  }
  const { list, ...pageInfo } = data
  // 可能list是null
  return [list || [], pageInfo]
}
