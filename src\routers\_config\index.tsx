import { RouteObject } from 'react-router-dom'
import { emptyRoute, lazyRoute } from './Loader'

const routesConfig: RouteObject[] = [
  {
    path: '/',
    element: lazyRoute(() => import('../../routers')),
    children: [
      {
        path: 'login',
        element: lazyRoute(() => import('../sign/Login')),
      },
      {
        index: true,
        element: lazyRoute(() => import('../projectManage/index')),
      },
      {
        path: 'projectManage',
        children: [
          {
            path: 'ProjectAdd',
            element: lazyRoute(() => import('../projectManage/ProjectAdd')),
          },
        ],
      },
      {
        path: 'manualReviewManager',
        children: [
          {
            path: '',
            element: lazyRoute(() => import('../manualReviewManager/index')),
          },
          {
            path: 'projectAllocation',
            element: lazyRoute(
              () => import('../manualReviewManager/ProjectAllocation')
            ),
          },
          {
            path: 'manualReview/:id',
            element: lazyRoute(() => import('../projectManage/ManualReview')),
          },
        ],
      },

      {
        path: 'ruleMould',
        element: lazyRoute(() => import('../ruleMould/index')),
      },
      {
        path: 'fieldLibrary',
        element: lazyRoute(() => import('../fieldLibrary/index')),
      },
      {
        path: 'userList',
        element: lazyRoute(() => import('../userList/index')),
      },
    ],
  },
  emptyRoute,
]

const demo: RouteObject[] = import.meta.env.DEV
  ? [
      {
        path: '/demo',
        children: [
          {
            path: 'icon',
            element: lazyRoute(() => import('../../components/demo/IconDemo')),
          },
        ],
      },
    ]
  : []
const routes = [...routesConfig, ...demo]
export default routes
