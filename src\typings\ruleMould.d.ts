declare interface RuleLibrary {
  id: string
  key: number //同id一样
  title: string
  children?: RuleMould[]
}

declare interface RuleMould {
  id: string
  libraryId: string
  title: string
  type: number //catalogue 条目 files 文件夹
  key: number //同id一样
}

declare interface FilesDisposeType {
  // 空白图片审查
  blank: boolean
  // 重复图片审查
  repeatImage: boolean
  // 文本方向
  houseAngle: boolean
  // 倾斜
  bias: boolean
  // 倾斜角容错
  rectify: string
  // 瑕疵
  flaw: boolean
  // 黑边
  edgeRemove: boolean
  // 污点容错
  stainValue?: number
  // 装订孔容错
  hole?: number
  // DPI检查
  dpi: boolean
  // DPI值
  dpiValue: string
  // 格式检查
  format: boolean
  formatList: string[] // 'JPEG', 'JPEG' ,'TIFF', 'PDF', 'GIF',  'RAW','BMP', 'FPX', 'PNG'
  // 数量统计
  counting: boolean
  // KB值检查
  kb: boolean
  // KB最小值
  minKB?: number
  // KB最大值
  maxKB?: number
  // 图像质量检查
  imageQuality?:boolean
  // 图像质量
  imageScore?:number
  // '空文件夹检查'
  blankFilesCheck: boolean
  // 篇幅统计
  pageSize: boolean
  // 条目数量图片一致性检查
  pieceContinuous: boolean
  // 图片页号连续性检查
  continuity: boolean
  // PDF文件质量审查
  // pdfImageUniformity: string
  // 污点
  stain: boolean
  // 装订孔
  bindingHole: boolean
  ofdUniformity: boolean
  reImageName:"",
  reImagePath:""
  damage:boolean,
}

declare interface RuleMouldConfig {
  id: string
  libraryId: string
  ruleAliasName?: string
  ruleCode?: string
  ruleDescribe?: string
  ruleFields?: string[]
  ruleFieldsName?: string[]
  ruleName?: string
  librarytype?: number
  ruleValue?: string
  createUserName?: string
  createTime?: string
  ruleDate?: string
}
