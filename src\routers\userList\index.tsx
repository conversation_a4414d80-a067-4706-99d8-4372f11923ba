import { Button, Input, Popconfirm, Select, Table } from 'antd'
import React, { useEffect, useState } from 'react'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import { useMutation, useQuery } from 'react-query'

import { getUserPage, saveUser } from '../../api/user'
import { extractResponse } from '../../api/util'
import { userType } from '../../typings/user'
import { userRole } from '../../constants/user'
import { useUserContext } from '../../hooks/useUser'
import CreateUser from '../../components/userList/CreateUser'
import EditPassword from '../../components/userList/EditPassword'

export default function UserList() {
  const [showCreateUser, setShowCreateUser] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [selectObj, setSelectObj] = useState<Record<string, any>>({
    pageNo: 1,
    search_EQ_aidosRole: '',
  })
  const [userId, seUserId] = useState<string>('')
  const { Option } = Select
  const [editTarget, setEditTarget] = useState<userType>()
  const [params, setParams] = useState<Record<string, any>>({
    pageNo: 1,
    pageSize: 10,
    search_CONTAINS_name: '',
    search_EQ_aidosRole: '',
  })
  const { user: currentUser } = useUserContext()

  console.log(currentUser)
  const { data, refetch } = useQuery(['account-page'], () =>
    extractResponse(() => getUserPage(params))()
  )

  const saveMutation = useMutation(saveUser, {
    onSuccess() {
      console.log(1)
      refetch()
    },
  })

  useEffect(() => {
    refetch()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [params])
  if (!currentUser) {
    return null
  }
  const paginationObj = {
    total: data?.total,
    showTotal: (total: any, range: any) =>
      `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
    defaultCurrent: 1,
    defaultPageSize: 10,
    onChange: (page: number) => {
      setParams(p => ({ ...p, pageNo: page }))
    },
    pageSizeOptions: ['5', '10', '50'],
    onShowSizeChange: (current: any, size: number) => {
      setParams(p => ({ ...p, pageSize: size }))
    },
    showQuickJumper: true,
    current: data?.pageNo,
  }

  const columns = [
    {
      title: '序号',
      render: (item: userType, _: any, index: number) => <div>{index + 1}</div>,
      key: 'id',
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '编号',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '类型',
      render: (item: userType) => <div>{userRole[item.aidosRole]}</div>,
      key: 'aidosRole',
    },
    {
      title: '状态',
      render: (item: userType) => (
        <div>{item.usable === 1 ? '启用' : '禁用'}</div>
      ),
      key: 'usable',
    },
    {
      title: '操作',
      dataIndex: '',
      key: 'x',
      render: (item: userType) => (
        <div className="flex">
          {item.aidosRole !== 'hide' ? (
            <>
              {(currentUser.aidosRole === 'superAdmin' ||
                currentUser.code === item.code) &&
              item.aidosRole !== 'superAdmin' ? (
                <>
                  <div
                    className="mr-4 cursor-pointer text-primary-default"
                    onClick={() => {
                      setEditTarget(item)
                      setShowCreateUser(true)
                    }}
                  >
                    编辑
                  </div>
                  <div
                    className="mr-4  cursor-pointer text-primary-default"
                    onClick={() => {
                      setShowPassword(true)
                      seUserId(item.code)
                    }}
                  >
                    密码
                  </div>
                </>
              ) : null}
            </>
          ) : null}

          {(item.aidosRole !== 'superAdmin' && item.usable === 1) ||
          currentUser.aidosRole === 'user' ? (
            <Popconfirm
              placement="bottomRight"
              title="你确定要删除吗？"
              onConfirm={() => saveMutation.mutate({ ...item, usable: 0 })}
              okText="确定"
              cancelText="取消"
              okType="danger"
              icon={<ExclamationCircleOutlined style={{ color: '#FF4D4F' }} />}
            >
              <div className="cursor-pointer text-primary-default">删除</div>
            </Popconfirm>
          ) : null}
        </div>
      ),
    },
  ]

  return (
    <div className=" px-6">
      <div className="h-16 mt-2 flex justify-end items-center">
        <Input
          className="h-8 "
          style={{ width: '160px', height: '32px', marginRight: '14px' }}
          placeholder="请输入姓名"
          value={selectObj.search_CONTAINS_name}
          onChange={e => {
            setSelectObj(p => ({ ...p, search_CONTAINS_name: e.target.value }))
          }}
          maxLength={40}
        />
        <Select
          className="w-[108px] h-8"
          value={selectObj.search_EQ_aidosRole}
          onChange={e => {
            setSelectObj(p => ({ ...p, search_EQ_aidosRole: e }))
          }}
          getPopupContainer={triggerNode => triggerNode.parentElement}
        >
          <Option value="superAdmin">超级管理员</Option>
          <Option value="">全部用户</Option>
          <Option value="user">操作员</Option>
          <Option value="admin">管理员</Option>
        </Select>
        <Button
          type="primary"
          className="ml-[14px]"
          onClick={() => setParams(selectObj)}
        >
          搜索
        </Button>
        <Button
          className="ml-6"
          onClick={() => {
            setShowCreateUser(true)
          }}
        >
          创建用户
        </Button>
      </div>
      <Table
        dataSource={data?.list}
        columns={columns}
        pagination={paginationObj}
      />

      <CreateUser
        data={editTarget}
        isOpen={showCreateUser}
        onClose={() => {
          setShowCreateUser(false)
          setEditTarget(undefined)
        }}
        refetch={refetch}
      />
      <EditPassword
        isOpen={showPassword}
        onClose={setShowPassword}
        code={userId}
      />
    </div>
  )
}
