package com.deeppaas.rule.biz.parser;

import com.deeppaas.rule.biz.action.RAction;
import com.deeppaas.rule.biz.action.RDataEntityDeleteAction;
import com.deeppaas.rule.biz.model.RConditionModel;
import com.fasterxml.jackson.databind.JsonNode;

/**
 * <AUTHOR>
 * @date 2022/6/14
 */
public class RDataEntityDeleteActionParser extends RActionParser{
    private static final String KEY_DATA_ENTITY = "dataEntity";
    private static final String KEY_PERMIT_CONDITION = "condition";
    public static RAction buildAction(JsonNode actionNode) {
        RDataEntityDeleteAction action = new RDataEntityDeleteAction();
        buildBaseInfo(action, actionNode);
        action.setDataEntity(actionNode.get(KEY_DATA_ENTITY).textValue());
        JsonNode conditionNode = actionNode.get(KEY_PERMIT_CONDITION);
        RConditionModel rConditionModel = RConditionModelParser.buildRConditionModel(conditionNode);
        action.setCondition(rConditionModel);
        return action;
    }
}
