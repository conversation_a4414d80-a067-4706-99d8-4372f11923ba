{"name": "liangjiang", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "start": "one-start", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"@ant-design/cssinjs": "^1.10.1", "@ant-design/icons": "^4.7.0", "antd": "^5.0.0", "axios": "^1.6.7", "classnames": "^2.3.1", "dayjs": "^1.11.2", "esm": "^3.2.25", "formik": "^2.2.9", "lodash-es": "^4.17.21", "node-fetch": "^3.3.1", "rc-picker": "^3.5.1", "react": "^17.0.2", "react-dom": "^17.0.2", "react-hot-toast": "^2.2.0", "react-hotkeys-hook": "^4.4.0", "react-query": "^3.34.16", "react-router-dom": "^6.2.2"}, "devDependencies": {"@types/lodash-es": "^4.17.4", "@types/node": "^17.0.21", "@types/react": "^17.0.33", "@types/react-dom": "^17.0.10", "@vitejs/plugin-react": "^1.0.7", "autoprefixer": "^10.4.4", "cz-conventional-changelog": "^3.3.0", "dotenv": "^16.0.0", "eslint-config-react-tsone": "^4.1.0", "less": "^4.1.2", "lint-staged": "^11.1.2", "one-start": "^0.6.1", "tailwindcss": "^3.0.23", "typescript": "^4.5.4", "vite": "^2.9.1", "yup": "^0.32.9"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx,json}": ["eslint --fix"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog", "disableScopeLowerCase": false, "disableSubjectLowerCase": false}}}