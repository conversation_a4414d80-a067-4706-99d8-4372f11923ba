import axios from 'axios'
import React, { useState } from 'react'
import { RcFile } from 'antd/lib/upload'
import { UploadFile } from '../components/UploadFolderFileModal/utils'
import { scanUpload } from '../api/project'

interface Props {
  activeConfig: CatalogueFile
  visible: boolean
  type: string
}

function useUploadFiles({ activeConfig, visible, type }: Props) {
  const [waitingKeyList, setWaitingKeyList] = React.useState<string[]>([])
  const [uploadFileMap, setUploadFileMap] = React.useState<
    Record<string, File>
  >({})
  const [uploadKey, setUploadKey] = React.useState<string>()
  const fetchKeyRef = React.useRef<string>()
  const [allKeyList, setAllKeyList] = React.useState<string[]>([])
  const [errorBlog, setErrorBlog] = useState<string[]>([])

  React.useEffect(() => {
    if (!visible) {
      setWaitingKeyList([])
      setUploadFileMap({})
      setUploadKey(undefined)
      setAllKeyList([])
      fetchKeyRef.current = undefined
    }
  }, [visible])

  React.useEffect(() => {
    if (!uploadKey && waitingKeyList.length) {
      setUploadKey(waitingKeyList[0])
      setWaitingKeyList(p => p.slice(1))
    }
  }, [uploadKey, waitingKeyList])
  React.useEffect(() => {
    if (uploadKey && uploadKey !== fetchKeyRef.current) {
      fetchKeyRef.current = uploadKey
      const fileItem = uploadFileMap[uploadKey]
      const formData = new FormData()
      formData.append('file', fileItem)
      formData.append('fileName', fileItem.webkitRelativePath)
      console.log({ fileItem })
      setUploadFileMap(p => ({
        ...p,
        [uploadKey]: { ...fileItem },
      }))
      scanUpload(
        activeConfig,
        formData,
        {
          headers: { 'Content-Type': 'multipart/form-data' },
          cancelToken: new axios.CancelToken(cancel => {
            setUploadFileMap(p => ({
              ...p,
              [uploadKey]: { ...fileItem, cancel },
            }))
          }),
          onUploadProgress(evt: any) {
            const progress = Math.round((evt.loaded * 100) / evt.total)
            if (progress === 100) {
              setUploadKey(undefined)
              setUploadFileMap(p => ({
                ...p,
                [uploadKey]: {
                  ...fileItem,
                  progress,
                },
              }))
            } else {
              // 不展示单个文件进度，没必要每次都更新进度
              // setUploadFileMap(p => ({
              //   ...p,
              //   [key]: { ...fileItem, progress },
              // }))
            }
          },
        },
        type
      ).catch(res => {
        setUploadKey(undefined)
        setUploadFileMap(p => ({
          ...p,
          [uploadKey]: { ...fileItem },
        }))
        if (res.code !== 200) {
          errorBlog.push(fileItem.name)

          setErrorBlog(p => [...p, fileItem.name])
        }
      })
    }
  }, [activeConfig, errorBlog, uploadFileMap, uploadKey])

  function doUpload(keyList: string[], fileMap: Record<string, File>) {
    setWaitingKeyList(keyList)
    setUploadFileMap(fileMap)
    setAllKeyList(keyList)
    fetchKeyRef.current = undefined
  }

  function removeFile(keyList: string[]) {
    // setWaitingKeyList(p => p.filter(key => !keyList.includes(key)))
    // setAllKeyList(p => p.filter(key => !keyList.includes(key)))
    // setUploadFileMap(p => {
    //   keyList.forEach(key => p[key].cancel?.())
    //   return p
    // })
    // setUploadKey(p => (p && keyList.includes(p) ? undefined : p))
    // if (fetchKeyRef.current && keyList.includes(fetchKeyRef.current)) {
    //   fetchKeyRef.current = undefined
    // }
  }

  const waitingWithUploadKeyList = uploadKey
    ? [uploadKey, ...waitingKeyList]
    : waitingKeyList

  return {
    doUpload,
    uploadFileMap,
    removeFile,
    waitingWithUploadKeyList,
    allKeyList,
    errorBlog,
    setErrorBlog,
  }
}

export default useUploadFiles
