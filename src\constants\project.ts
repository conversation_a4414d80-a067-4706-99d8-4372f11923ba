export enum ProcessStatus {
  PENDING,
  PROCESSING,
  SUSPEND,
  DONE,
  ERROR,
}

//任务进度
export const StatusText = {
  [ProcessStatus.PENDING]: '未开始',
  [ProcessStatus.PROCESSING]: '进行中',
  [ProcessStatus.SUSPEND]: '已暂停',
  [ProcessStatus.DONE]: '已完成',
  [ProcessStatus.ERROR]: '失败',
}

// 人工进度
export const ManStatusText = {
  [ProcessStatus.PENDING]: '待抽查',
  [ProcessStatus.PROCESSING]: '抽查中',
  [ProcessStatus.DONE]: '已完成',
  [ProcessStatus.SUSPEND]: '',
}

export const ruleConfigTypeValue = {
  0: 0,
  1: 1,
  2: 2,
  3: 3,
}

export const ruleConfigTypeText = {
  [ruleConfigTypeValue[0]]: '案卷条目',
  [ruleConfigTypeValue[1]]: '卷内目录',
  [ruleConfigTypeValue[2]]: '图像',
  [ruleConfigTypeValue[3]]: 'PDF',
}
