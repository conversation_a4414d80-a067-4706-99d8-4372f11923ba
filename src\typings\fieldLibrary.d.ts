declare interface FieldLibraryType {
  id: string
  libraryName: string
}

declare interface FieldType {
  id: string
  fieldName: string
  fieldDescribe: string
  createUserName: string
  createUserId: number
  createTime: string
}

declare type UserCheckType = {
  dataKey: string
  imageCount: number
  count: number
  index: number
  isPass?: number //1合格
  errorType?: string
  remarks?: string
  checkStatus: number
  checkUserName?: string
  id?: string
  checkUser?: string
  allocationState?: number
  taskId: string
}

declare type PieceImageType = {
  images: { key: string; value: string }[]
  dataKey: string
  taskId: string
  partNumber: string
}

declare type ImageType = {
  key: string
  value: string
  piece: string
}


declare type TaskDataParamType = {
  [key: string]:string
}

declare type TaskDataType={
  param:TaskDataParamType,
  task_id:string|undefined,
  dataKey:string|undefined,
}
