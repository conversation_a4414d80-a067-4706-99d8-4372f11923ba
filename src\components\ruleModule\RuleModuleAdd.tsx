import { Modal, Input, Form, Select } from 'antd'
import FormItem from 'antd/es/form/FormItem'
import { Key } from 'antd/lib/table/interface'
import React, { useEffect } from 'react'
import { useMutation } from 'react-query'
import { libraryRuleSave } from '../../api/fieldLibrary'

type FieldAddType = {
  open: boolean
  close: React.Dispatch<React.SetStateAction<boolean>>
  activeLibrary?: Key
  refetch: () => void
}

const typeList = [
  { label: '图像', value: 2 },
  { label: '条目', value: 1 },
]

const defaultValue = {
  libraryName: '',
  librarytype: '',
}

export default function RuleModuleAdd({
  open,
  close,
  activeLibrary,
  refetch,
}: FieldAddType) {
  const form = Form.useForm()[0]

  useEffect(() => {
    if (open) {
      form.setFieldsValue(defaultValue)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open])

  const libraryRuleSaveMutation = useMutation(libraryRuleSave, {
    onSuccess(res) {
      refetch()
    },
  })

  function submit(values: any) {
    if (activeLibrary) {
      libraryRuleSaveMutation.mutate({
        ...values,
        libraryParentId: activeLibrary,
      })
    }
    close(false)
  }

  return (
    <Modal
      title="新建规则模板"
      open={open}
      onCancel={() => {
        close(false)
      }}
      onOk={() => {
        form.submit()
      }}
    >
      <Form
        form={form}
        onFinish={submit}
        labelCol={{ span: 7 }}
        style={{ width: '380px' }}
      >
        <FormItem
          label="规则模板"
          rules={[{ required: true, message: '请输入规则模板名称' }]}
          name="libraryName"
        >
          <Input maxLength={40} />
        </FormItem>
        <FormItem
          label="模板类型"
          name="librarytype"
          rules={[{ required: true, message: '请选择模板类型' }]}
        >
          <Select options={typeList} key="value" />
        </FormItem>
      </Form>
    </Modal>
  )
}
