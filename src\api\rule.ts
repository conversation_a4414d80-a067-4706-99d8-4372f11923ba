//rule/save

import request from './requestExactness'
import { PageData, Result } from './types'

export function ruleSave(formData: {
  libraryId: string
  ruleValue: string
  ruleType: number
  id?: string
  ruleCode: string
}) {
  return request.post<Result<RuleLibrary[]>>('/rule/save', formData)
}

// getById
export function getRuleById(id: string, ruleType: number) {
  return request.get<Result<RuleMouldConfig[]>>(
    '/rule/getByLibraryIdRuleType?id=' + id + '&ruleType=' + ruleType
  )
}

export function getRulePage(
  pageNo: number,
  pageSize: number,
  params: Record<string, any>
) {
  const param = JSON.parse(JSON.stringify(params))
  for (const item in param) {
    if (param[item] === '') {
      delete params[item]
    }
  }
  delete params.pageNo
  delete params.pageSize

  return request.post<Result<PageData<RuleMouldConfig>>>('/rule/page', {
    pageNo,
    pageSize,
    param: params,
  })
}

export function ruleDelById(id: string) {
  return request.get<Result<string>>('/rule/delById?id=' + id)
}

export function ruleDelByIds(formData: string[]) {
  return request.post<Result<string>>('/rule/delByIds', formData)
}

export function getRuleByLibraryId(id: string) {
  return request.get<Result<RuleMouldConfig[]>>('/rule/getByLibraryId?id=' + id)
}
