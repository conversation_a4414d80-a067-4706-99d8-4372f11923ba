import { Modal, Table, Image as Img, Button } from 'antd'
import { ColumnsType } from 'antd/lib/table'
import React, { useEffect, useRef, useState } from 'react'
import { useQuery } from 'react-query'
import classNames from 'classnames'
import { EyeOutlined } from '@ant-design/icons'
import { getErrorImageDetail } from '../../api/project'
import { extractResponse } from '../../api/util'
import error from '../../style/img/error.png'
import { getInitialImageSize, loadImage } from '../../utils/image'

type ImageInfoFunType = {
  close: React.Dispatch<React.SetStateAction<boolean>>
  activeImageInfo?: ImageRuleInfoType[]
  projectId: string
}

const colorMap = {
  污点: '#52C41A',
  装订孔: '#BF5AF2',
  黑边: '#1677FF',
  KB值检查: '#FF4D4F',
}
type colorLabel = '污点' | '装订孔' | '黑边' | 'KB值检查'

export default function ImageInfo({
  close,
  activeImageInfo,
  projectId,
}: ImageInfoFunType) {
  const divRef = useRef<HTMLDivElement>(null)
  const boxRef = useRef<HTMLDivElement>(null)
  const [activeImage, setActiveImage] = useState<ImageRuleInfoType>()
  const [imageStyle, setImageStyle] = useState<Record<string, number>>()
  const [infoStyle, setInfoStyle] = useState<Record<string, number>>()
  const [styleRatio, setStyleRatio] = useState<Record<string, number>>()
  const [currentPage, setCurrentPage] = useState(1)

  const { data } = useQuery(
    ['getErrorImageDetail', activeImage],
    activeImage
      ? extractResponse(() =>
          getErrorImageDetail({
            taskId: projectId,
            imageName: activeImage.fieldName,
          })
        )
      : () => undefined,
    {
      enabled: !!activeImage,
    }
  )

  useEffect(() => {
    if (divRef?.current) {
      const current = divRef?.current
      const style = {
        left: current.getBoundingClientRect().x + 10,
        top: current.getBoundingClientRect().y + 10,
      }
      setInfoStyle(style)
    }
  }, [imageStyle, divRef, activeImage])

  useEffect(() => {
    if (boxRef.current) {
      boxRef.current.addEventListener('click', e => {
        if (!divRef.current?.contains(e.target as HTMLElement) && activeImage) {
          setActiveImage(undefined)
        }
      })
    }
  }, [boxRef, activeImage])

  useEffect(() => {
    if (activeImage) {
      loadImage(`/api/exactness/tool/` + activeImage?.fieldName).then(img => {
        const style = getInitialImageSize(img.width, img.height, {
          width: window.innerWidth,
          height: window.innerHeight - 40,
        })
        console.log(style.width / img.width)
        setStyleRatio({
          widthRatio: Number((style.width / img.width).toFixed(2)),
          heightRatio: Number((style.height / img.height).toFixed(2)),
        })
        setImageStyle(style)
      })
    }
  }, [activeImage])

  const columns: ColumnsType<ImageRuleInfoType> = [
    {
      title: '序号',
      render: (item, _, index) => index + 1,
      key: 'id',
    },
    {
      title: '图片',
      render: (item: ImageRuleInfoType) => (
        <Img
          src={`/api/exactness/tool/smart/${item.fieldName}`}
          onClick={() => {
            console.log(item.fieldName)
            if (item.fieldName) {
              setActiveImage(item)
            }
          }}
          className="max-w-[50px] max-h-[50px]"
          preview={{ visible: false, mask: <EyeOutlined /> }}
        />
      ),
      key: 'fieldName',
    },
    {
      title: '路径',
      render: (item: ImageRuleInfoType) => item.dataKey,
      key: 'dataKey',
    },
    // {
    //   title: '错误数量/值',
    //   render: (item: ImageRuleInfoType) => null,
    //   key: 'errorFileName',
    // },
    {
      title: '更新时间',
      render: (item: ImageRuleInfoType) => item.createTime,
      key: 'createTime',
    },
  ]

  function showValue(item: ImageRuleInfoType) {
    const value = JSON.parse(item.errorCoordinate || '')
    if (typeof value === 'object') {
      return value.length
    } else if (typeof value === 'boolean') {
      return value ? '有' : '无'
    } else {
      return value
    }
  }

  function SelectReact({
    value,
    ruleName,
  }: {
    value: string
    ruleName: string
  }) {
    if (!value && !styleRatio) {
      console.log(111111)
      return null
    }
    const arr = JSON.parse(value)
    if (typeof arr !== 'object') {
      return null
    }

    function mathStyle(list: number[]) {
      console.log({ list })
      const style = {
        left: list[0] * styleRatio?.widthRatio!,
        top: list[1] * styleRatio?.heightRatio!,
        width:
          list[2] * styleRatio?.widthRatio! - list[0] * styleRatio?.widthRatio!,
        height:
          list[3] * styleRatio?.heightRatio! -
          list[1] * styleRatio?.heightRatio!,
      }
      return style
    }

    return arr.map((item: number[]) => (
      <div
        className={`border-2 absolute`}
        style={{
          ...mathStyle(item),
          borderColor: colorMap[ruleName as colorLabel],
        }}
      />
    ))
  }

  const paginationObj = {
    current: currentPage,
    // pageSizeOptions: [10],
    showSizeChanger: false,
    onChange: (page: number) => {
      setCurrentPage(page)
    },
  }

  return (
    <div>
      <Modal
        title="图片列表"
        open={!!activeImageInfo}
        onOk={() => {}}
        onCancel={() => {
          close(false)
          setActiveImage(undefined)
          setCurrentPage(1)
        }}
        width={960}
        footer={[
          <Button
            key="submit"
            type="primary"
            onClick={() => {
              close(false)
              setCurrentPage(1)

              setActiveImage(undefined)
            }}
          >
            确认
          </Button>,
        ]}
      >
        <Table
          dataSource={activeImageInfo}
          columns={columns}
          pagination={paginationObj}
          className="mt-2 !max-w-[960] w-[960]"
          rowKey={record => record.id}
        />
        {activeImage ? (
          <div
            className=" fixed top-0"
            style={{
              position: 'fixed',
              top: 0,
              right: 0,
              bottom: 0,
              left: 0,
              zIndex: 1000,
              height: '100%',
              backgroundColor: 'rgba(0, 0, 0, 0.25)',
            }}
            onClick={() => {}}
            ref={boxRef}
          >
            <div
              // className="flex items-center justify-center"
              ref={divRef}
              style={imageStyle}
              className="relative top-5 left-0 right-0 bottom-0 m-auto"
            >
              <Img
                src={`/api/exactness/tool/${activeImage.fieldName}`}
                preview={false}
                style={imageStyle}
              />
              <div
                className="bg-white bg-opacity-50 w-40 min-h-[288px] fixed border border-dashed p-3"
                style={infoStyle}
              >
                <div className=" text-[#FF4D4F]">标准检测结果</div>
                {data?.map(item => (
                  <div className="flex mt-2">
                    <div
                      className={classNames('w-20 text-right')}
                      style={{ color: colorMap[item.ruleName as colorLabel] }}
                    >
                      {item.ruleName}：
                    </div>
                    <div className="ml-1">{showValue(item)}</div>
                  </div>
                ))}

                <img
                  src={error}
                  className="absolute -top-6 -right-12 w-[120px]"
                  alt=""
                />
              </div>
              {data?.map(rect => (
                <SelectReact
                  value={rect.errorCoordinate}
                  ruleName={rect.ruleName}
                />
              ))}
            </div>
          </div>
        ) : null}
      </Modal>
    </div>
  )
}
