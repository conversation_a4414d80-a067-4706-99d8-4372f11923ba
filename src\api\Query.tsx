import { QueryClient, QueryClientProvider } from 'react-query'
import { toast } from 'react-hot-toast'
import { PropsWithChildren } from 'react'
import React from 'react'
import { API_CODE } from './constant'

function catchError(error: any) {
  if (error) {
    if (error.data?.code === API_CODE.UNAUTHORIZED) {
      window.location.replace(
        `/login?original=${encodeURIComponent(
          window.location.pathname + window.location.search
        )}`
      )
    }

    if (error.data?.code === API_CODE.RUNTIME_ERROR) {
      toast.error(error.data?.message || '请求错误', { position: 'top-right' })
      return
    }

    toast.error(error.data?.message || '请求错误')
  }
}

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      refetchOnWindowFocus: false,
      refetchInterval: false,
      onSettled(_, error: any) {
        catchError(error)
      },
    },
    mutations: {
      onSettled(_, error: any) {
        catchError(error)
      },
    },
  },
})

export default function Query({ children }: PropsWithChildren<any>) {
  return (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
}
