import React from 'react'
import CloseIcon from '../icons/CloseIcon'
import CloudUploadIcon from '../icons/CloudUploadIcon'
import ConnectLineIcon from '../icons/ConnectLineIcon'
import ContentDrawIcon from '../icons/ContentDrawIcon'
import GetAPIIcon from '../icons/GetAPIIcon'
import ImageCheckIcon from '../icons/ImageCheckIcon'
import ImageIdentifyIcon from '../icons/ImageIdentifyIcon'
import MessageSyncIcon from '../icons/MessageSyncIcon'
import OpenWindowIcon from '../icons/OpenWindowIcon'
import PathLineIcon from '../icons/PathLineIcon'
import RuleMouldIcon from '../icons/RuleMouldIcon'
import TaskListenIcon from '../icons/TaskListenIcon'
import TemplateAdd from '../icons/TemplateAdd'
import UserListIcon from '../icons/UserListIcon'
import WayLineIcon from '../icons/WayLineIcon'
import FileOutlinedIcon from '../icons/FileOutlined'
import FolderOutlinedIcon from '../icons/FolderOutlined'
import EditOutlinedIcon from '../icons/EditOutlined'
import DeleteOutlinedIcon from '../icons/DeleteOutlined'

export default function IconDemo() {
  return (
    <div className="flex flex-wrap text-red-500">
      <CloseIcon className="w-8 h-8 fill-current" />
      <CloudUploadIcon className="w-8 h-8 fill-current" />
      <ContentDrawIcon className="w-8 h-8 fill-current" />
      <GetAPIIcon className="w-8 h-8 fill-current" />
      <ImageCheckIcon className="w-8 h-8 fill-current" />
      <ImageIdentifyIcon className="w-8 h-8 fill-current" />
      <MessageSyncIcon className="w-8 h-8 fill-current" />
      <OpenWindowIcon className="w-8 h-8 fill-current" />
      <PathLineIcon className="w-8 h-8 fill-current" />
      <TaskListenIcon className="w-8 h-8 fill-current" />
      <TemplateAdd className="w-8 h-8 fill-current" />
      <RuleMouldIcon className="w-8 h-8 fill-current" />
      <WayLineIcon className="w-8 h-8 fill-current" />
      <ConnectLineIcon className="w-8 h-8 fill-current" />
      <UserListIcon className="w-8 h-8 fill-current" />
      <FolderOutlinedIcon className="w-8 h-8 fill-current" />
      <FileOutlinedIcon className="w-8 h-8 fill-current" />
      <EditOutlinedIcon className="w-8 h-8 fill-current" />
      <DeleteOutlinedIcon className="w-8 h-8 fill-current" />
    </div>
  )
}
