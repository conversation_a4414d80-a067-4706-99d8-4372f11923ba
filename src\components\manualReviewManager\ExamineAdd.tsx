import { Modal, Transfer, message } from 'antd'
import React, { useEffect, useState } from 'react'
import { useMutation, useQuery } from 'react-query'
import { TransferDirection } from 'antd/es/transfer'
import { getUserPage } from '../../api/user'
import { extractResponse } from '../../api/util'
import {
  userCheckGroupDelTaskId,
  userCheckGroupGetByTaskId,
  userCheckGroupSaves,
} from '../../api/project'

type ExamineAddType = {
  close: () => void
  activeTask?: ProjectType
}

export default function ExamineAdd({ close, activeTask }: ExamineAddType) {
  const [params] = useState<Record<string, any>>({
    pageNo: 1,
    pageSize: 99999999,
  })
  const [userList, setUserList] = useState<{ title: string; key: string }[]>()

  const [targetKeys, setTargetKeys] = useState<string[]>([])
  const [selectedKeys, setSelectedKeys] = useState<string[]>([])

  useEffect(() => {
    if (activeTask) {
      setSelectedKeys([])
    }
  }, [activeTask])

  const onChange = (
    nextTargetKeys: string[],
    direction: TransferDirection,
    moveKeys: string[]
  ) => {
    console.log('targetKeys:', nextTargetKeys)
    console.log('direction:', direction)
    console.log('moveKeys:', moveKeys)
    setTargetKeys(nextTargetKeys)
  }

  const onSelectChange = (
    sourceSelectedKeys: string[],
    targetSelectedKeys: string[]
  ) => {
    console.log('sourceSelectedKeys:', sourceSelectedKeys)
    console.log('targetSelectedKeys:', targetSelectedKeys)
    setSelectedKeys([...sourceSelectedKeys, ...targetSelectedKeys])
  }

  useQuery(
    ['account-page'],
    () => extractResponse(() => getUserPage(params))(),
    {
      onSuccess(res) {
        if (res.list) {
          const arr = res.list.map(item => {
            return { title: item.name, key: item.id }
          })
          setUserList(arr)
        }
      },
    }
  )

  useQuery(
    ['userCheckGroupGetByTaskId', activeTask?.id],
    () => extractResponse(() => userCheckGroupGetByTaskId(activeTask?.id!))(),
    {
      enabled: !!activeTask?.id,
      onSuccess(res) {
        console.log(res)
        const list = res.map(item => item.userCheckId)
        setTargetKeys(list)
      },
    }
  )

  const userCheckGroupSavesMutation = useMutation(userCheckGroupSaves, {
    onSuccess(res) {
      message.success('添加成功')
      close()
    },
  })

  const userCheckGroupDelTaskIdMutation = useMutation(userCheckGroupDelTaskId, {
    onSuccess(res) {},
  })

  function save() {
    const list = targetKeys.map(item => {
      const user = userList?.find(user => user.key === item)
      return {
        userCheckId: item,
        taskId: activeTask?.id!,
        userCheckName: user!.title,
      }
    })
    userCheckGroupDelTaskIdMutation.mutateAsync(activeTask?.id!).then(() => {
      userCheckGroupSavesMutation.mutate(list)
    })
  }

  return (
    <Modal
      title="添加审核"
      open={!!activeTask}
      onCancel={() => {
        close()
      }}
      onOk={() => {
        save()
      }}
      className="h-[418px]"
    >
      <div className="mt-6 mb-2">任务名称：{activeTask?.taskName}</div>
      <div className="flex justify-center">
        <Transfer
          dataSource={userList}
          titles={['人员名单', '当前审核人']}
          targetKeys={targetKeys}
          selectedKeys={selectedKeys}
          onChange={onChange}
          onSelectChange={onSelectChange}
          // onScroll={onScroll}
          render={item => item.title}
          className=" tran-h mb-6 mx-auto"
        />
      </div>
    </Modal>
  )
}
