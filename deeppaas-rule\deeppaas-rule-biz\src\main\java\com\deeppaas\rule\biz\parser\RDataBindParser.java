package com.deeppaas.rule.biz.parser;

import com.deeppaas.common.data.enums.SimpleDataType;
import com.deeppaas.rule.biz.databind.RBindTargetType;
import com.deeppaas.rule.biz.databind.RDataBind;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/6/14
 */
public class RDataBindParser {
    private static final String KEY_DATA_BIND = "dataBind";
    private static final String TARGET_TYPE = "targetType";

    private static final String TARGET_DATA_TYPE = "dataType";
    private static final String TARGET_VALUE = "value";
    /**
     * 如果是模型类型则存在自动绑定
     */
    private static final String FIELDS = "fields";
    /**
     * 字段编号
     */
    private static final String FIELD_CODE = "code";

    public static RDataBind buildDataBind(JsonNode dataBindNode) {
        if(dataBindNode==null){
            return null;
        }
        String typeName = RBindTargetType.VOID.name();
        if(dataBindNode.get(TARGET_TYPE)!=null){
            typeName = dataBindNode.get(TARGET_TYPE).textValue();
        }
        String dataTypeName = dataBindNode.get(TARGET_DATA_TYPE).textValue();
        SimpleDataType dataType = SimpleDataType.valueOf(dataTypeName);
        RBindTargetType targetType = RBindTargetType.valueOf(typeName);
        JsonNode targetValueNode = dataBindNode.get(TARGET_VALUE);
        Map<String, RDataBind> children = buildChildren(dataBindNode.get(FIELDS));
        Object targetValue = switch (targetType) {
            case CONST -> getNodeData(targetValueNode);
            case VARS, PARAM, EXPR -> targetValueNode.textValue();
            case FUN -> RFunctionParser.buildFunction(targetValueNode);
            default -> null;
        };
        return new RDataBind(targetType, targetValue, dataType, children);
    }

    private static Object getNodeData(JsonNode constValueNode) {
        if(constValueNode.isNumber()){
            return constValueNode.decimalValue();
        }else if (constValueNode.isBoolean()){
            return constValueNode.booleanValue();
        }else {
            return constValueNode.textValue();
        }
    }

    private static Map<String, RDataBind> buildChildren(JsonNode fieldsNode) {
        if (fieldsNode!=null && fieldsNode.isArray()) {
            Map<String, RDataBind> fieldBindMap = new HashMap<>();
            Iterator<JsonNode> fieldIterator = fieldsNode.elements();
            while (fieldIterator.hasNext()) {
                JsonNode fieldBindNode = fieldIterator.next();
                String fieldCode = fieldBindNode.get(FIELD_CODE).textValue();
                JsonNode bindNode = fieldBindNode.get(KEY_DATA_BIND);
                RDataBind fieldBind = buildDataBind(bindNode);
                fieldBindMap.put(fieldCode, fieldBind);
            }
            return fieldBindMap;
        }
        return null;
    }

}
