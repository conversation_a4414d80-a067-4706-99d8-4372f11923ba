import React, { useState } from 'react'
import { <PERSON><PERSON>er<PERSON>outer, useRoutes } from 'react-router-dom'
import { useQuery } from 'react-query'

import { StyleProvider } from '@ant-design/cssinjs'
import { ConfigProvider } from 'antd'
import locale from 'antd/lib/locale/zh_CN'
import routesConfig from './routers/_config'
import { getVersion } from './api/user'
import { extractResponse } from './api/util'
import versionContext from './hooks/useVersion'
import '../src/style/index.css'
// import '../src/style/indexXicheng.css'
function Routes() {
  const elements = useRoutes(routesConfig)
  const [version, setVersion] = useState('default')

  useQuery(
    ['getVersion'],
    extractResponse(() => getVersion()),
    {
      onSuccess(res) {
        setVersion(res)
        // if (res === 'default') {
        //   import('../src/style/index.css')
        // } else {
        //   import('../src/style/indexXicheng.css')
        // }
      },
    }
  )

  return (
    <ConfigProvider
      locale={locale}
      theme={{
        token: {
          colorPrimary: version === 'default' ? '#FF770d' : '#1890FF',
        },
      }}
    >
      <StyleProvider hashPriority="high">
        <versionContext.Provider value={{ version: version }}>
          {elements}
        </versionContext.Provider>
      </StyleProvider>
    </ConfigProvider>
  )
}

function App() {
  return (
    <BrowserRouter>
      <Routes />
    </BrowserRouter>
  )
}

export default App
