import {
  DownOutlined,
  FormOutlined,
  SolutionOutlined,
  UserOutlined,
} from '@ant-design/icons'
import { Avatar, Dropdown, Menu, MenuProps } from 'antd'
import React, { useEffect, useState } from 'react'
import { useQuery, useMutation } from 'react-query'
import { NavLink, Outlet, useLocation, useNavigate } from 'react-router-dom'
import classnames from 'classnames'
import { getCurrentUser, logout } from '../api/user'
import { extractResponse } from '../api/util'
import FieldLibraryIcon from '../components/icons/FieldLibraryIcon'
import RuleMouldIcon from '../components/icons/RuleMouldIcon'
import UserListIcon from '../components/icons/UserListIcon'
import UserContext from '../hooks/useUser'
// import logo from '../style/img/logo.png'
import logo from '../style/img/newlogo.png'
import xichengLogo from '../style/img/xichengLogo.png'

import { getAppVersion } from '../api/project'
import { useVersionContext } from '../hooks/useVersion'

const defaultMenu: Record<string, any>[] = [
  {
    path: '/',
    title: '任务管理',
    icon: <SolutionOutlined className="w-4 h-4 fill-current " />,
    hide: 'admin',
  },
  {
    path: '/manualReviewManager',
    title: '人工审查',
    icon: <FormOutlined className="w-4 h-4 fill-current " />,
    hide: 'user',
  },
  {
    path: '/ruleMould',
    title: '规则模板',
    icon: <RuleMouldIcon className="w-4 h-4 fill-current " />,
    hide: 'admin',
  },
  {
    path: '/fieldLibrary',
    title: '字段库表',
    icon: <FieldLibraryIcon className="w-4 h-4 fill-current " />,
    hide: 'admin',
  },
  {
    path: '/userList',
    title: '用户列表',
    icon: <UserListIcon className="w-4 h-4 fill-current " />,
    hide: 'admin',
  },
]

type MenuItem = Required<MenuProps>['items'][number]

export default function RouteIndex() {
  const [isLayout, setIsLayout] = useState(false)
  const location = useLocation()
  const navigate = useNavigate()

  const [menu, setMenu] = useState<MenuItem[]>([])
  const [isAdvancedForbidden, setIsAdvancedForbidden] = useState<boolean>()
  const [selectedKeys, setSelectedKeys] = useState<string[]>([])
  const { version } = useVersionContext()

  const { data: user } = useQuery('me', extractResponse(getCurrentUser), {
    enabled: location.pathname !== '/login',
  })
  const [isLogin, setIsLogin] = useState(
    location.pathname === '/login' ||
      location.pathname.indexOf('/workbench/projects/ReviseImage') > -1
  )

  useEffect(() => {
    // return navigate.listen(location => {
    setIsLogin(location.pathname === '/login')
    // })
  }, [location])

  useEffect(() => {
    if (location.pathname.indexOf('/projectManage') > -1) {
      setSelectedKeys(['/'])
    } else if (location.pathname.indexOf('manualReviewManager') > -1) {
      setSelectedKeys(['/manualReviewManager'])
    } else {
      setSelectedKeys([location.pathname])
    }
  }, [location.pathname])

  useEffect(() => {
    !!/\/(login)/i.test(location.pathname)
      ? setIsAdvancedForbidden(true)
      : setIsAdvancedForbidden(false)
  }, [location.pathname, user])

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const routeList = ['/login']

  const logoutMutation = useMutation(logout, {
    onSuccess() {
      window.location.replace(
        `/login?original=${encodeURIComponent(
          window.location.pathname + window.location.search
        )}`
      )
    },
  })

  // getAppVersion
  const { data: versionNumber } = useQuery(
    ['getAppVersion'],
    extractResponse(() => getAppVersion())
  )

  const list = (
    <Menu className="w-22" key={1}>
      <Menu.Item
        onClick={() => {
          logoutMutation.mutate()
        }}
      >
        退出
      </Menu.Item>
    </Menu>
  )

  useEffect(() => {
    if (
      user?.aidosRole === 'user' &&
      !/\/(manualReviewManager|manualReview)/i.test(location.pathname)
    ) {
      window.location.replace(`/manualReviewManager`)
    }
  }, [location.pathname, user?.aidosRole])

  useEffect(() => {
    const list: MenuItem[] = []
    if (version !== 'default') {
      defaultMenu.forEach((item, index) => {
        if (item.path === '/userList') {
          delete defaultMenu[index]
        }
        if (item.path === '/manualReviewManager') {
          delete defaultMenu[index]
        }
      })
    }
    defaultMenu.forEach(item => {
      const childList: MenuItem[] = []
      if (item.hide === 'admin' && user?.aidosRole === 'user') {
        return
      }
      if (item.children) {
        item.children.forEach((menu: Record<string, any>) => {
          childList.push({ label: menu.title, key: menu.path, icon: menu.icon })
        })
      }

      return list.push({
        label: item.title,
        key: item.path,
        icon: item.icon,
        children: childList.length > 0 ? childList : undefined,
      })
    })
    setMenu(list)
  }, [user?.aidosRole, version])

  useEffect(() => {
    routeList.indexOf(location.pathname) >= 0
      ? setIsLayout(false)
      : setIsLayout(true)
  }, [location.pathname, routeList])

  // if (!user) {
  //   return null
  // }
  return (
    <UserContext.Provider value={{ user }}>
      {isLayout ? (
        <div>
          <div className="h-14 bg-white relative">
            <NavLink
              to="/"
              onClick={() => {
                setSelectedKeys(['/'])
              }}
            >
              <img
                src={version === 'default' ? logo : xichengLogo}
                width="173"
                height="36"
                alt=""
                className="absolute top-[11px] left-6"
              />
            </NavLink>
            {/* <div className="flex items-center absolute right-[166px] top-5">
              <div className="rounded-full w-1.5 h-1.5 bg-red-600 mr-1" />
              <div className="text-xs">已连接</div>
            </div> */}
            {version === 'default' ? (
              <Dropdown overlay={list}>
                <div className="absolute right-[26px] flex items-center h-full cursor-pointer">
                  <Avatar icon={<UserOutlined />} />
                  <div className="ml-2 mr-2">{user?.name}</div>
                  <DownOutlined />
                </div>
              </Dropdown>
            ) : null}
          </div>
          <div className="flex">
            {version === 'default' ? (
              <Menu
                items={menu}
                defaultSelectedKeys={[location.pathname]}
                mode="inline"
                // className="h-full"
                selectedKeys={selectedKeys}
                style={{ width: 200, minHeight: `calc(100vh - 96px)` }}
                onClick={item => {
                  console.log(item)
                  navigate(item.key)
                  setSelectedKeys([item.key])
                }}
              />
            ) : (
              <Menu
                // items={menu}
                className="menuRouter"
                defaultSelectedKeys={[location.pathname]}
                mode="inline"
                selectedKeys={selectedKeys}
                style={{
                  width: 274,
                  minHeight: `calc(100vh - 56px)`,
                  // background: `linear-gradient(156deg, #0326ac 15%, #055de3 56%, #05a3eb 98%)`,
                  backgroundColor: '#0d0c48',
                  color: '#b1c4e5',
                  fontSize: 22,
                  textAlign: 'left',
                }}
                onClick={item => {
                  console.log(item)
                  navigate(item.key)
                  setSelectedKeys([item.key])
                }}
              >
                {defaultMenu.map(items => (
                  <Menu.Item key={items?.path} className="px-0 !h-[52px]">
                    {items.title}
                  </Menu.Item>
                ))}
              </Menu>
            )}

            <div className="m-4 flex-1 bg-white pb-4 h-[calc(100vh - 72px)]">
              <Outlet />
            </div>
          </div>
        </div>
      ) : null}
      {isAdvancedForbidden ? <Outlet /> : null}

      <div
        className={classnames('text-sm', {
          'text-center text-gray-secondary pb-5': !isLogin,
          'fixed left-1/2 bottom-5 transform -translate-x-1/2 text-gray-secondary':
            isLogin,
        })}
      >
        <span>©2023 酷豹科技</span>
        <span className="ml-2">v{versionNumber || '1.0.0'}</span>
      </div>
    </UserContext.Provider>
  )
}
