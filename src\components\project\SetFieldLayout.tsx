import React, { useEffect, useMemo, useState } from 'react'
import classNames from 'classnames'
// import { useToasts } from 'react-toast-notifications'
import { Drawer, Button, Input, message, Space } from 'antd'
import {
  QueryObserverResult,
  RefetchOptions,
  RefetchQueryFilters,
  useMutation,
} from 'react-query'
import { SaveFieldLayoutType, saveFieldLayout } from '../../api/project'

// import {
//   saveFieldLayout,
//   FieldLayout,
//   saveFieldSpecialLayout,
// } from '../../../api/workbench'
// import { ProjectBindFieldDO } from '../../../typings/system'

// export type FieldLayout = {
//   layout: string
//   id: number
//   userId: number
//   tableId: number
// }

interface Props {
  taskId: string
  isOpenFormLayout?: boolean
  fieldList: { label: string; value: string }[]
  onClose(refresh?: boolean): void
  layoutData: SaveFieldLayoutType | undefined
  activeConfig: string
  layoutRefetch: <TPageData>(
    options?: (RefetchOptions & RefetchQueryFilters<TPageData>) | undefined
  ) => Promise<QueryObserverResult<SaveFieldLayoutType | undefined, unknown>>
}
const ColWidth = 260
const ColHeight = 32
const gridSize = 30
const gap = 10
const gridTable = Array.from({ length: gridSize })

export interface FieldParams {
  id?: number
  name?: string
  col?: number
  row?: number
  cols?: number
}
let initDragFlag = false

export default function SetFieldLayout({
  isOpenFormLayout,
  fieldList,
  onClose,
  taskId,
  layoutData,
  activeConfig,
  layoutRefetch,
}: Props) {
  //定义位置信息
  const [position, setPosition] = useState<Array<FieldParams>>([])
  // 使用字段名称代替显示布局上的字段名称，解决后期修改了字段名称造成显示不一致的问题
  const fieldNames = useMemo(() => {
    return fieldList.reduce<Record<string, string>>((map, item) => {
      map[item.label] = item.label
      return map
    }, {})
  }, [fieldList])

  useEffect(() => {
    setPosition(JSON.parse(layoutData?.layout || '[]'))
  }, [layoutData?.layout, isOpenFormLayout])

  const [colValue, setColValue] = useState<number | string>('')
  // const { addToast } = useToasts()

  //是否可以选中字段
  const [isGetFieldAble, setIsGetFieldAble] = React.useState(false)

  //选中空格子的下标
  const [readyIndex, setReadyIndex] = useState(-1)

  //选择一个空格子
  function selectEmptyGrid(index: number) {
    setReadyIndex(index)
    //设置可以选择字段了
    setIsGetFieldAble(true)
  }

  //左边选中的字段
  const [activeFieldName, setActiveFieldName] = useState<string>()

  //已选择的字段数组
  const [positionNames, setPositionNames] = useState<Array<string>>([])
  //根据选择的字段设置激活的位置
  const activePosition = activeFieldName
    ? position.find(item => {
        return item.name === activeFieldName
      })
    : undefined

  //选择一个字段
  function selectField(field: { label: string; value: string }) {
    if (readyIndex < 0 || readyIndex > gridSize - 1) {
      return
    }
    //如果还没有选择底图

    if (!isGetFieldAble) {
      message.warning('请选择一个空格子')
      return
    }
    //如果已经被选择过，那么不能再次被选择

    if (positionNames.indexOf(field.label) !== -1) {
      message.warning('该字段已被设置')
      return
    }
    //如果底图上已经有了对应的字段
    // if (positionNames.length === baseIds.length) {
    //   message.warning('请选择一个空白区域进行放置')
    //   return
    // }
    console.log({ readyIndex })
    setPosition([
      ...position,
      {
        name: field.label,
        col: (readyIndex % 3) + 1,
        row: Math.floor(readyIndex / 3) + 1,
        cols: 1,
      },
    ])
    setActiveFieldName(field.label)
    //关闭不让连续点击选择字段
    setIsGetFieldAble(false)
    setColValue(1)
  }

  useEffect(() => {
    const result: string[] = []
    position.forEach(item => {
      if (item.name) {
        result.push(item.name)
      }
    })
    setPositionNames([...result])
  }, [position])

  const fields = fieldList
  //选择格子上面一个字段
  function pickField(item: FieldParams) {
    setActiveFieldName(item.name)
    setColValue(Number(item.cols))
  }

  //获取最大宽度，先实现，很啰嗦，再优化
  function getMaxCols() {
    //获取同一行的数据
    // 获取到当前行
    const col = activePosition?.col || 3

    //根据当前行获取到一行所有的数据
    const sameRow: FieldParams[] = position.filter(item => {
      return item.row === activePosition?.row
    })
    //判断他之后有多少个空格，一旦遇到实格，就返回
    //  for (let i = col - 1; i < 3; i++) {
    //   if()
    // }
    let maxCol = 1

    if (col === 2) {
      const result: FieldParams[] = sameRow.filter(item => {
        return item.col === 3
      })

      if (result.length === 0) {
        maxCol = 2
      }
    }
    if (col === 1) {
      const result2: FieldParams[] = sameRow.filter(item => {
        return item.col === 2
      })
      const result3: FieldParams[] = sameRow.filter(item => {
        return item.col === 3
      })
      if (result2.length === 0) {
        maxCol = 2
      }
      if (result2.length === 0 && result3.length === 0) {
        maxCol = 3
      }
    }
    return maxCol
  }
  //设置宽度
  function updateFieldCols(cols: number = 1) {
    const maxCol = getMaxCols()
    if (cols > maxCol) {
      // addToast(`最多可设置${maxCol}列`)
      return
    }
    //根据输入框是否是合法的值，再进行列宽设置
    if (cols >= 1 && cols <= 3) {
      setPosition(
        position.map(item => {
          if (item.name === activePosition?.name) {
            return {
              ...item,
              cols,
            }
          }
          return item
        })
      )
    }

    if (cols >= 1 && cols <= 3) {
      setColValue(cols)
    } else {
      setColValue('')
      return
    }
  }
  //清除空格，清除空格子上的字段
  function cleanEmptyGrid() {
    //清除空格
    setReadyIndex(-1)
    if (!activePosition?.name) return
    //删除选中空格上的字段
    setPosition([
      ...position.filter(item => item.name !== activePosition?.name),
    ])
    setActiveFieldName(undefined)
  }
  //保存布局设置
  const saveFieldLayoutMutation = useMutation(saveFieldLayout, {
    onSuccess() {
      message.warning('保存成功')
      onClose(true)
      layoutRefetch()
    },
  })

  function saveLayout() {
    saveFieldLayoutMutation.mutate({
      ...layoutData,
      taskId: taskId,
      layout: JSON.stringify(position),
      typeId: activeConfig,
    })
  }

  useEffect(() => {
    //拖拽
    const drag = {
      init: () => {
        drag.run()
      },
      run: () => {
        const layoutDragItem: any = document.querySelectorAll('.layoutDragItem')
        const layoutDrag: any = document.querySelector('.layoutDrag')

        //存放拖拽体的位置
        const aPs: any = []
        for (let i = 0, len = layoutDragItem.length; i < len; i++) {
          aPs.push([layoutDragItem[i].offsetLeft, layoutDragItem[i].offsetTop])
        }
        layoutDrag.addEventListener('mousedown', drag)
        document.addEventListener('mousemove', drag)
        document.addEventListener('mouseup', drag)

        let toggle = false //默认没有点中元素
        let x1: number,
          y1: number,
          startX: number,
          startY: number,
          ele: any,
          zIndex = 1,
          goalEle: any
        function drag(ev: any) {
          ev = ev || window.event //对象bom
          ev.preventDefault()
          ev.stopPropagation()
          ev.cancelBubble = true
          switch (ev.type) {
            case 'mousedown':
              ev.stopPropagation()
              if (
                ev.target.parentNode.className.indexOf('layoutDragItem') !== -1
              ) {
                ele = ev.target.parentNode
                ele.style.zIndex = zIndex++
                x1 = ev.clientX
                y1 = ev.clientY
                startX = ev.target.parentNode.offsetLeft
                startY = ev.target.parentNode.offsetTop
                toggle = true
              }

              break
            case 'mousemove':
              ev.stopPropagation()
              if (toggle) {
                const x2 = ev.clientX
                const y2 = ev.clientY
                const nowX = startX + x2 - x1
                const nowY = startY + y2 - y1

                ele.style.left = nowX + 'px'
                ele.style.top = nowY + 'px'

                //碰撞逻辑

                let par = layoutDrag.offsetParent
                //首先把自己本身的相加
                let totalLeft = layoutDrag.offsetLeft
                let totalTop = layoutDrag.offsetTop
                while (par) {
                  if (navigator.userAgent.indexOf('MSIE 8.0') === -1) {
                    //不是IE8我们才进行累加父级参照物的边框
                    totalTop += par.clientTop
                    totalLeft += par.clientLeft
                  }
                  //把父级参照物的偏移相加
                  totalLeft += par.offsetLeft
                  totalTop += par.offsetTop

                  par = par.offsetParent
                }

                const xR = x2 - totalLeft //元素的x轴位置
                const yR = y2 - totalTop
                for (let j = 0, len = layoutDragItem.length; j < len; j++) {
                  layoutDragItem[j].style.transform = ''

                  if (
                    ele !== layoutDragItem[j] &&
                    xR > layoutDragItem[j].offsetLeft &&
                    xR < layoutDragItem[j].offsetLeft + ColWidth &&
                    yR > layoutDragItem[j].offsetTop &&
                    yR < layoutDragItem[j].offsetTop + ColHeight
                  ) {
                    layoutDragItem[j].style.transform = 'scale(1.05)'
                    goalEle = layoutDragItem[j]
                  }
                }
              }
              break
            case 'mouseup':
              ev.stopPropagation()
              ev.cancelBubble = true
              toggle = false
              if (goalEle) {
                ele.style.left = goalEle.offsetLeft + 'px'
                ele.style.top = goalEle.offsetTop + 'px'
                goalEle.style.left = startX + 'px'
                goalEle.style.top = startY + 'px'
                goalEle.style.transform = ''
                goalEle = ''
                ele = ''
              }
              if (ele) {
                ele.style.left = startX + 'px'
                ele.style.top = startY + 'px'
                ele = null
              }

              break
            default:
              break
          }
        }
      },
    }
    if (position.length > 1 && initDragFlag) {
      drag.init()
      initDragFlag = false
    }
  }, [position])

  return (
    <Drawer
      width={1300}
      title={'调整布局'}
      open={isOpenFormLayout}
      onClose={() => onClose()}
      extra={
        <Space>
          <Button onClick={() => onClose()}>取消</Button>
          <Button type="primary" onClick={saveLayout}>
            保存
          </Button>
        </Space>
      }
      maskClosable={false}
    >
      <div className="px-4 pb-9 h-full">
        <div className="flex  space-x-5 h-full ">
          {/* 左边字段 */}
          <div className="flex flex-col  w-[170px]   bg-[#F6F9FE] rounded-lg overflow-hidden">
            <p className="h-[38px] bg-[#E1EAFE] rounded pl-5 text-base font-black leading-10">
              选择字段
            </p>
            <div className=" p-5 pt-7 flex-1 overflow-auto space-y-3">
              {fields.map(field => {
                return (
                  <p
                    key={field.label}
                    onClick={() => selectField(field)}
                    className={classNames(
                      'border w-32 h-8 rounded text-gray-secondary text-xs text-center leading-8 truncate px-1 bg-white cursor-pointer',
                      {
                        '!bg-[#FAB963] text-white':
                          positionNames.indexOf(field.label) !== -1,
                        '!bg-[#548FF5] text-white':
                          activePosition?.name === field?.label,
                      }
                    )}
                  >
                    {field.label}
                  </p>
                )
              })}
            </div>
          </div>
          {/*中间 位置与格子外层div */}
          <div
            className=" flex-col rounded p-3  bg-[#F6F9FE] w-[836px] overflow-auto"
            // style={{ width: ColWidth * 3 + gap * 3 }}
          >
            <div className="relative flex flex-wrap layoutDrag">
              {/* 位置信息 */}
              {position.map(item => {
                console.log({ item })

                return (
                  <div
                    key={item.name}
                    className="bg-[#E1EAFE] layoutDragItem rounded h-8 absolute text-white text-base left-1 top-1 flex items-center"
                    style={{
                      height: ColHeight,
                      width: item.cols
                        ? item.cols * ColWidth + (item.cols - 1) * gap
                        : 0,
                      top: item.row
                        ? (item.row - 1) * ColHeight +
                          gap / 2 +
                          (item.row - 1) * gap
                        : 0,
                      left: item.col
                        ? (item.col - 1) * ColWidth +
                          gap / 2 +
                          (item.col - 1) * gap
                        : 0,
                      backgroundColor:
                        activePosition?.name === item?.name
                          ? '#548FF5'
                          : '#FAB963',
                    }}
                    onClick={() => {
                      pickField(item)
                    }}
                  >
                    {/* <SixPoints className="w-4 h-4 fill-current ml-3 mr-1" /> */}
                    <span
                      className="font-medium text-xs truncate flex-1 ml-3"
                      // style={{ pointerEvents: 'none' }}
                    >
                      {fieldNames[item?.name!] || item?.name}
                    </span>
                  </div>
                )
              })}

              {/* 底图网格 */}
              {gridTable.map((_, index) => {
                return (
                  <div
                    key={index}
                    style={{
                      margin: gap / 2,
                      width: ColWidth,
                      height: ColHeight,
                      background: readyIndex === index ? '#548FF5' : '#E1EAFE',
                    }}
                    className="cursor-pointer  rounded h-8 items-center text-white text-base flex"
                    onClick={() => selectEmptyGrid(index)}
                  />
                )
              })}
              {/*
              <div
                className=" bg-[#548FF5]  h-8 text-center leading-8 text-xs text-white mt-1 w-full ml-1 cursor-pointer"
                onClick={updateRow}
              >
                增加多行
              </div> */}
            </div>
          </div>
          {/* 表单字段 */}
          <div className="flex flex-col  w-[170px]   bg-[#F6F9FE] rounded-lg">
            <p className="h-[38px] bg-[#E1EAFE] rounded pl-5 text-base font-black leading-10">
              字段布局设置
            </p>
            <div className="p-5 pt-7">
              <div>
                <p>当前字段</p>
                <p className="text-sm leading-8 h-8 text-white truncate bg-[#CCCCCC] pl-4 mt-1 rounded">
                  {fieldNames[activePosition?.name!]}
                </p>
              </div>
              <div className="mt-6">
                <p>占据数列</p>
                <p className="text-sm leading-8 h-8 text-white rounded">
                  <Input
                    autoComplete="off"
                    className="w-full  mt-1"
                    value={colValue}
                    onChange={e => updateFieldCols(parseInt(e.target.value))}
                    onBlur={e => updateFieldCols(parseInt(e.target.value))}
                  />
                </p>
              </div>
              <div className="mt-6">
                <Button onClick={cleanEmptyGrid}>删除</Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Drawer>
  )
}
