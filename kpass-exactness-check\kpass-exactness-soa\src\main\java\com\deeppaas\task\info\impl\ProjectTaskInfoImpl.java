package com.deeppaas.task.info.impl;

import com.deeppaas.FileEnum;
import com.deeppaas.account.api.client.context.LocalUserContext;
import com.deeppaas.account.api.client.model.LocalUser;
import com.deeppaas.common.data.jdbc.JCondition;
import com.deeppaas.common.exception.RunException;
import com.deeppaas.common.helper.BoolHelper;
import com.deeppaas.common.helper.StringHelper;
import com.deeppaas.common.model.PageData;
import com.deeppaas.common.model.PageSearch;
import com.deeppaas.result.service.TaskErrorResultService;
import com.deeppaas.task.config.entity.ProjectTaskConfigDO;
import com.deeppaas.task.config.service.ProjectTaskConfigService;
import com.deeppaas.task.data.service.ProjectTaskFormDataService;
import com.deeppaas.task.data.service.ProjectTaskImageDataService;
import com.deeppaas.task.info.convert.ProjectTaskInfoConvert;
import com.deeppaas.task.info.dao.ProjectTaskInfoDao;
import com.deeppaas.task.info.dto.ProjectTaskInfoDTO;
import com.deeppaas.task.info.entity.ProjectTaskInfoDO;
import com.deeppaas.task.info.service.ProjectTaskInfoService;
import com.deeppaas.userCheck.group.dao.UserCheckGroupDao;
import com.deeppaas.userCheck.group.dto.UserCheckGroupDTO;
import com.deeppaas.userCheck.group.service.UserCheckGroupService;
import com.deeppaas.work.enums.TaskStatusEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProjectTaskInfoImpl implements ProjectTaskInfoService {

    @Autowired
    private ProjectTaskInfoDao projectTaskInfoDao;
    @Autowired
    private ProjectTaskConfigService projectTaskConfigService;
    @Autowired
    private ProjectTaskFormDataService projectTaskFormDataService;
    @Autowired
    private ProjectTaskImageDataService projectTaskImageDataService;

    @Lazy
    @Autowired
    private TaskErrorResultService taskErrorResultService;
    @Autowired
    private ProjectTaskInfoConvert projectTaskInfoConvert;
    @Lazy
    @Autowired
    private UserCheckGroupService userCheckGroupService;

    @Override
    public PageData<ProjectTaskInfoDTO> page(PageSearch pageSearch) {
        LocalUser localUser= LocalUserContext.getLocalUser();
        String userId=localUser.getId();
        JCondition condition = JCondition.ofPageParam(pageSearch.getSearchParams());
        Page<ProjectTaskInfoDO> page = projectTaskInfoDao.queryForPage(condition, pageSearch.getPageable());
        List<ProjectTaskInfoDTO> lits=projectTaskInfoConvert.toDto(page.getContent());
        List<String>  ids=lits.stream().map(ProjectTaskInfoDTO::getId).collect(Collectors.toList());
        List<UserCheckGroupDTO> userCheckGroupDTOS=userCheckGroupService.findByTaskIdIn(ids);
        lits.forEach(item->{
            UserCheckGroupDTO groupDTO= userCheckGroupDTOS.stream().filter(it->Objects.equals(item.getId(),it.getTaskId())&&Objects.equals(it.getUserCheckId(),userId)).findFirst().orElse(null);
        if (groupDTO!=null)
            item.setIsShow(1);
        });

        //判断是不是本人的
        return PageData.init(page,lits);
    }

    @Override
    public List<ProjectTaskInfoDTO> list() {
        return projectTaskInfoConvert.toDto(projectTaskInfoDao.findAll());
    }

    @Override
    public List<ProjectTaskInfoDTO> findByProjectId(String projectId) {
        return projectTaskInfoConvert.toDto(projectTaskInfoDao.findByProjectId(projectId));
    }

    @Override
    public ProjectTaskInfoDTO findTop1ByProjectId(String projectId) {
        return projectTaskInfoConvert.toDto(projectTaskInfoDao.findTop1ByProjectId(projectId));
    }

    @Override
    public ProjectTaskInfoDTO findById(String id) {
        return projectTaskInfoConvert.toDto(projectTaskInfoDao.getById(id));
    }

    @Override
    public ProjectTaskInfoDTO findByTaskName(String taskName) {
        return projectTaskInfoConvert.toDto(projectTaskInfoDao.findTop1ByTaskName(taskName));
    }


    @Override
    @Transactional
    public ProjectTaskInfoDO save(ProjectTaskInfoDO projectTaskInfoDO, LocalUser localUser) {
        String id = projectTaskInfoDO.getId();
        if (StringHelper.isEmpty(id)) {
            projectTaskInfoDO.setTaskTimeStamp(String.valueOf(System.currentTimeMillis()));
            projectTaskInfoDO.setCreateUserName(localUser.getName());
            projectTaskInfoDO.setCreateUserId(localUser.getId());
            projectTaskInfoDO.getCreateUserId();
            ProjectTaskInfoDO list = projectTaskInfoDao.findTop1ByTaskName(projectTaskInfoDO.getTaskName());
            if (list!=null) {
                throw RunException.error("任务名称已存在！");
            }

        }

        projectTaskInfoDao.save(projectTaskInfoDO);
        if (StringHelper.isEmpty(id)) {
            ProjectTaskConfigDO projectTaskConfigDO = new ProjectTaskConfigDO();
            projectTaskConfigDO.setTaskId(projectTaskInfoDO.getId());
            projectTaskConfigService.save(projectTaskConfigDO);
        }
        return projectTaskInfoDO;
    }
    @Override
    @Transactional
    public ProjectTaskInfoDO saveAndConfig(ProjectTaskInfoDO projectTaskInfoDO) {
        String id = projectTaskInfoDO.getId();
        if (StringHelper.isEmpty(id)) {
            projectTaskInfoDO.setTaskTimeStamp(String.valueOf(System.currentTimeMillis()));
            projectTaskInfoDO.setTaskStatus(TaskStatusEnums.READY.getNum());
            projectTaskInfoDO.setTaskProgress(new BigDecimal(0));
        }

        projectTaskInfoDao.save(projectTaskInfoDO);
        if (StringHelper.isEmpty(id)) {
            ProjectTaskConfigDO projectTaskConfigDO = new ProjectTaskConfigDO();
            projectTaskConfigDO.setTaskId(projectTaskInfoDO.getId());
            projectTaskConfigService.save(projectTaskConfigDO);
        }
        return projectTaskInfoDO;
    }

    @Override
    @Transactional
    public ProjectTaskInfoDO save(ProjectTaskInfoDTO projectTaskInfoDTO) {
        ProjectTaskInfoDO projectTaskInfoDO= projectTaskInfoConvert.toDo(projectTaskInfoDTO);
        String id = projectTaskInfoDO.getId();
        if (StringHelper.isEmpty(id)) {
            projectTaskInfoDO.setTaskTimeStamp(String.valueOf(System.currentTimeMillis()));
            projectTaskInfoDO.setTaskStatus(TaskStatusEnums.READY.getNum());
            projectTaskInfoDO.setTaskProgress(new BigDecimal(0));
        }

        projectTaskInfoDao.save(projectTaskInfoDO);
        return projectTaskInfoDO;
    }

    @Override
    @Transactional
    public void delById(String id) {
        projectTaskInfoDao.deleteById(id);
    }

    @Override
    @Transactional
    public void delByIds(List<String> ids) {
        projectTaskInfoDao.deleteAllById(ids);
    }

    static Integer i = 0;


    @Override
    public synchronized void updateTaskProcess(String taskId, BigDecimal process,Integer type) {
        // 🔍 进度更新方法入口日志
        System.out.println("🚀🚀🚀 === 进度更新方法调用 === 🚀🚀🚀");
        System.out.println(String.format("📋 任务ID: %s", taskId));
        System.out.println(String.format("📊 传入进度: %s", process));
        System.out.println(String.format("🏷️ 更新类型: %s (%s)", type, getTypeDescription(type)));

        ProjectTaskInfoDO taskInfoDO = projectTaskInfoDao.getById(taskId);
        Integer status = taskInfoDO.getTaskStatus();
        BigDecimal taskProgress = new BigDecimal("0");
        BigDecimal imageProgress = taskInfoDO.getImageProgress();  // 🔧 修复：应该是getImageProgress()
        BigDecimal excelProgress = taskInfoDO.getExcelProgress();

        System.out.println(String.format("📊 数据库当前状态 - 图像: %s%%, Excel: %s%%, 总进度: %s%%, 状态: %s",
            taskInfoDO.getImageProgress(), excelProgress, taskInfoDO.getTaskProgress(), status));

        if (imageProgress == null)
            imageProgress =new BigDecimal("0");

        if(Objects.equals(type, FileEnum.C.getNum())){
            int count= projectTaskImageDataService.count(taskId);
            int countNum=projectTaskImageDataService.countSuccess(taskId, BoolHelper.INT_TRUE);

            // 🔍 详细的图像进度计算日志
            System.out.println("🔍🔍🔍 === 图像进度更新详情 === 🔍🔍🔍");
            System.out.println(String.format("📋 任务ID: %s", taskId));
            System.out.println(String.format("📊 图像总数: %d", count));
            System.out.println(String.format("✅ 已完成数: %d", countNum));
            System.out.println(String.format("📈 完成率: %.2f%%", (double)countNum / count * 100));

            imageProgress=new BigDecimal(String.valueOf(Double.valueOf(countNum))).divide(new BigDecimal(count),2, RoundingMode.HALF_UP).multiply(new BigDecimal(100));

            System.out.println(String.format("🎯 计算的图像进度: %s%%", imageProgress));
            System.out.println("🔍🔍🔍 === 图像进度计算完成 === 🔍🔍🔍");
        } else if(Objects.equals(type, FileEnum.ARCHIVE_ELEMENTS.getNum())){
            // 🔍 动态计算档案要素检查进度，确保Excel总进度达到100%
            BigDecimal oldExcelProgress = excelProgress;
            BigDecimal targetExcelProgress = new BigDecimal("100");
            BigDecimal archiveElementsProgress = targetExcelProgress.subtract(oldExcelProgress);

            // 直接设置Excel进度为100%
            excelProgress = targetExcelProgress;

            System.out.println("🔍🔍🔍 === 档案要素检查进度更新（动态计算） === 🔍🔍🔍");
            System.out.println(String.format("📋 任务ID: %s", taskId));
            System.out.println(String.format("📊 Excel进度更新前: %s%%", oldExcelProgress));
            System.out.println(String.format("🎯 目标Excel进度: %s%%", targetExcelProgress));
            System.out.println(String.format("➕ 档案要素检查贡献: %s%%", archiveElementsProgress));
            System.out.println(String.format("📊 Excel进度更新后: %s%%", excelProgress));
            System.out.println("🔍🔍🔍 === 档案要素进度更新完成 === 🔍🔍🔍");
        } else {
            excelProgress=excelProgress.add(process);
        }
        // 🎯 重新计算总进度：图像50% + Excel50%
        BigDecimal imageWeight = imageProgress.multiply(new BigDecimal("0.5")); // 图像权重50%
        BigDecimal excelWeight = excelProgress.min(new BigDecimal("100"))       // Excel进度最多100%
                                    .multiply(new BigDecimal("0.5"));           // Excel权重50%

        taskProgress = imageWeight.add(excelWeight);

        // 🔍 详细的总进度计算日志
        System.out.println("🎯🎯🎯 === 总进度计算详情 === 🎯🎯🎯");
        System.out.println(String.format("📊 图像进度: %s%% → 权重后: %s%%", imageProgress, imageWeight));
        System.out.println(String.format("📊 Excel进度: %s%% → 限制后: %s%% → 权重后: %s%%",
            excelProgress, excelProgress.min(new BigDecimal("100")), excelWeight));
        System.out.println(String.format("🎯 最终总进度: %s%%", taskProgress));
        System.out.println("🎯🎯🎯 === 总进度计算完成 === 🎯🎯🎯");
        if (taskProgress.intValue() >= 100) {
            taskProgress = new BigDecimal(100);
            status = TaskStatusEnums.FINISH.getNum();
        }
        String taskTimeStamp = taskInfoDO.getTaskTimeStamp();

        // 🔍 数据库更新前的最终状态日志
        System.out.println("💾💾💾 === 准备更新数据库 === 💾💾💾");
        System.out.println(String.format("📊 最终状态 - 图像: %s%%, Excel: %s%%, 总进度: %s%%, 状态: %s",
            imageProgress, excelProgress, taskProgress, status));

        int v = projectTaskInfoDao.updateTaskProcess(taskId, taskProgress, String.valueOf(System.currentTimeMillis()), taskTimeStamp, LocalDateTime.now(), status,imageProgress,excelProgress);

        if (v == 0 && i < 10) {
            i++;
            log.error("taskId======>【" + taskId + "】锁冲突！！！！！！！！！！！！！！！！！！！");
            System.out.println("⚠️ 数据库更新锁冲突，重试中...");
            updateTaskProcess(taskId, process,type);
        } else {
            i = 0;
            System.out.println("✅ 数据库更新成功");
            System.out.println("🚀🚀🚀 === 进度更新完成 === 🚀🚀🚀");
        }
    }

    @Override
    public void delAllById(String taskId) {
        projectTaskInfoDao.deleteById(taskId);
        projectTaskConfigService.delByTaskId(taskId);
        projectTaskFormDataService.delTaskId(taskId);
        projectTaskImageDataService.delTaskId(taskId);
        taskErrorResultService.delTaskId(taskId);
    }

    /**
     * 获取更新类型的描述
     */
    private String getTypeDescription(Integer type) {
        if (type == null) return "未知";
        if (Objects.equals(type, FileEnum.C.getNum())) return "图像检查";
        if (Objects.equals(type, FileEnum.A.getNum())) return "Excel检查";
        if (Objects.equals(type, FileEnum.ARCHIVE_ELEMENTS.getNum())) return "档案要素检查";
        return "其他类型";
    }

}
