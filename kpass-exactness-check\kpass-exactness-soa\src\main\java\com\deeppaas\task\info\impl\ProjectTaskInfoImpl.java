package com.deeppaas.task.info.impl;

import com.deeppaas.FileEnum;
import com.deeppaas.account.api.client.context.LocalUserContext;
import com.deeppaas.account.api.client.model.LocalUser;
import com.deeppaas.common.data.jdbc.JCondition;
import com.deeppaas.common.exception.RunException;
import com.deeppaas.common.helper.BoolHelper;
import com.deeppaas.common.helper.StringHelper;
import com.deeppaas.common.model.PageData;
import com.deeppaas.common.model.PageSearch;
import com.deeppaas.result.service.TaskErrorResultService;
import com.deeppaas.task.config.entity.ProjectTaskConfigDO;
import com.deeppaas.task.config.service.ProjectTaskConfigService;
import com.deeppaas.task.data.service.ProjectTaskFormDataService;
import com.deeppaas.task.data.service.ProjectTaskImageDataService;
import com.deeppaas.task.info.convert.ProjectTaskInfoConvert;
import com.deeppaas.task.info.dao.ProjectTaskInfoDao;
import com.deeppaas.task.info.dto.ProjectTaskInfoDTO;
import com.deeppaas.task.info.entity.ProjectTaskInfoDO;
import com.deeppaas.task.info.service.ProjectTaskInfoService;
import com.deeppaas.userCheck.group.dao.UserCheckGroupDao;
import com.deeppaas.userCheck.group.dto.UserCheckGroupDTO;
import com.deeppaas.userCheck.group.service.UserCheckGroupService;
import com.deeppaas.work.enums.TaskStatusEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProjectTaskInfoImpl implements ProjectTaskInfoService {

    @Autowired
    private ProjectTaskInfoDao projectTaskInfoDao;
    @Autowired
    private ProjectTaskConfigService projectTaskConfigService;
    @Autowired
    private ProjectTaskFormDataService projectTaskFormDataService;
    @Autowired
    private ProjectTaskImageDataService projectTaskImageDataService;

    @Lazy
    @Autowired
    private TaskErrorResultService taskErrorResultService;
    @Autowired
    private ProjectTaskInfoConvert projectTaskInfoConvert;
    @Lazy
    @Autowired
    private UserCheckGroupService userCheckGroupService;

    @Override
    public PageData<ProjectTaskInfoDTO> page(PageSearch pageSearch) {
        LocalUser localUser= LocalUserContext.getLocalUser();
        String userId=localUser.getId();
        JCondition condition = JCondition.ofPageParam(pageSearch.getSearchParams());
        Page<ProjectTaskInfoDO> page = projectTaskInfoDao.queryForPage(condition, pageSearch.getPageable());
        List<ProjectTaskInfoDTO> lits=projectTaskInfoConvert.toDto(page.getContent());
        List<String>  ids=lits.stream().map(ProjectTaskInfoDTO::getId).collect(Collectors.toList());
        List<UserCheckGroupDTO> userCheckGroupDTOS=userCheckGroupService.findByTaskIdIn(ids);
        lits.forEach(item->{
            UserCheckGroupDTO groupDTO= userCheckGroupDTOS.stream().filter(it->Objects.equals(item.getId(),it.getTaskId())&&Objects.equals(it.getUserCheckId(),userId)).findFirst().orElse(null);
        if (groupDTO!=null)
            item.setIsShow(1);
        });

        //判断是不是本人的
        return PageData.init(page,lits);
    }

    @Override
    public List<ProjectTaskInfoDTO> list() {
        return projectTaskInfoConvert.toDto(projectTaskInfoDao.findAll());
    }

    @Override
    public List<ProjectTaskInfoDTO> findByProjectId(String projectId) {
        return projectTaskInfoConvert.toDto(projectTaskInfoDao.findByProjectId(projectId));
    }

    @Override
    public ProjectTaskInfoDTO findTop1ByProjectId(String projectId) {
        return projectTaskInfoConvert.toDto(projectTaskInfoDao.findTop1ByProjectId(projectId));
    }

    @Override
    public ProjectTaskInfoDTO findById(String id) {
        return projectTaskInfoConvert.toDto(projectTaskInfoDao.getById(id));
    }

    @Override
    public ProjectTaskInfoDTO findByTaskName(String taskName) {
        return projectTaskInfoConvert.toDto(projectTaskInfoDao.findTop1ByTaskName(taskName));
    }


    @Override
    @Transactional
    public ProjectTaskInfoDO save(ProjectTaskInfoDO projectTaskInfoDO, LocalUser localUser) {
        String id = projectTaskInfoDO.getId();
        if (StringHelper.isEmpty(id)) {
            projectTaskInfoDO.setTaskTimeStamp(String.valueOf(System.currentTimeMillis()));
            projectTaskInfoDO.setCreateUserName(localUser.getName());
            projectTaskInfoDO.setCreateUserId(localUser.getId());
            projectTaskInfoDO.getCreateUserId();
            ProjectTaskInfoDO list = projectTaskInfoDao.findTop1ByTaskName(projectTaskInfoDO.getTaskName());
            if (list!=null) {
                throw RunException.error("任务名称已存在！");
            }

        }

        projectTaskInfoDao.save(projectTaskInfoDO);
        if (StringHelper.isEmpty(id)) {
            ProjectTaskConfigDO projectTaskConfigDO = new ProjectTaskConfigDO();
            projectTaskConfigDO.setTaskId(projectTaskInfoDO.getId());
            projectTaskConfigService.save(projectTaskConfigDO);
        }
        return projectTaskInfoDO;
    }
    @Override
    @Transactional
    public ProjectTaskInfoDO saveAndConfig(ProjectTaskInfoDO projectTaskInfoDO) {
        String id = projectTaskInfoDO.getId();
        if (StringHelper.isEmpty(id)) {
            projectTaskInfoDO.setTaskTimeStamp(String.valueOf(System.currentTimeMillis()));
            projectTaskInfoDO.setTaskStatus(TaskStatusEnums.READY.getNum());
            projectTaskInfoDO.setTaskProgress(new BigDecimal(0));
        }

        projectTaskInfoDao.save(projectTaskInfoDO);
        if (StringHelper.isEmpty(id)) {
            ProjectTaskConfigDO projectTaskConfigDO = new ProjectTaskConfigDO();
            projectTaskConfigDO.setTaskId(projectTaskInfoDO.getId());
            projectTaskConfigService.save(projectTaskConfigDO);
        }
        return projectTaskInfoDO;
    }

    @Override
    @Transactional
    public ProjectTaskInfoDO save(ProjectTaskInfoDTO projectTaskInfoDTO) {
        ProjectTaskInfoDO projectTaskInfoDO= projectTaskInfoConvert.toDo(projectTaskInfoDTO);
        String id = projectTaskInfoDO.getId();
        if (StringHelper.isEmpty(id)) {
            projectTaskInfoDO.setTaskTimeStamp(String.valueOf(System.currentTimeMillis()));
            projectTaskInfoDO.setTaskStatus(TaskStatusEnums.READY.getNum());
            projectTaskInfoDO.setTaskProgress(new BigDecimal(0));
        }

        projectTaskInfoDao.save(projectTaskInfoDO);
        return projectTaskInfoDO;
    }

    @Override
    @Transactional
    public void delById(String id) {
        projectTaskInfoDao.deleteById(id);
    }

    @Override
    @Transactional
    public void delByIds(List<String> ids) {
        projectTaskInfoDao.deleteAllById(ids);
    }

    static Integer i = 0;


    @Override
    public synchronized void updateTaskProcess(String taskId, BigDecimal process,Integer type) {
        ProjectTaskInfoDO taskInfoDO = projectTaskInfoDao.getById(taskId);
        Integer status = taskInfoDO.getTaskStatus();
        BigDecimal taskProgress = new BigDecimal("0");
        BigDecimal imageProgress =taskInfoDO.getTaskProgress();
        BigDecimal excelProgress = taskInfoDO.getExcelProgress();

        if (imageProgress == null)
            imageProgress =new BigDecimal("0");

        if(Objects.equals(type, FileEnum.C.getNum())){
            int count= projectTaskImageDataService.count(taskId);
            int countNum=projectTaskImageDataService.countSuccess(taskId, BoolHelper.INT_TRUE);
            imageProgress=new BigDecimal(String.valueOf(Double.valueOf(countNum))).divide(new BigDecimal(count),2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
        } else if(Objects.equals(type, FileEnum.ARCHIVE_ELEMENTS.getNum())){
            // 🔍 档案要素检查完成时，将excelProgress设为100
            excelProgress = new BigDecimal("100");
            System.out.println("🔍 档案要素检查完成，Excel进度更新为100%");
        } else {
            excelProgress=excelProgress.add(process);
        }
        taskProgress=taskProgress.add(imageProgress).add(excelProgress);
        if (taskProgress.intValue() >= 100) {
            taskProgress = new BigDecimal(100);
            status = TaskStatusEnums.FINISH.getNum();
        }
        String taskTimeStamp = taskInfoDO.getTaskTimeStamp();
        int v = projectTaskInfoDao.updateTaskProcess(taskId, taskProgress, String.valueOf(System.currentTimeMillis()), taskTimeStamp, LocalDateTime.now(), status,imageProgress,excelProgress);
        if (v == 0 && i < 10) {
            i++;
            log.error("taskId======>【" + taskId + "】锁冲突！！！！！！！！！！！！！！！！！！！");
            updateTaskProcess(taskId, process,type);
        } else {
            i = 0;
        }
    }

    @Override
    public void delAllById(String taskId) {
        projectTaskInfoDao.deleteById(taskId);
        projectTaskConfigService.delByTaskId(taskId);
        projectTaskFormDataService.delTaskId(taskId);
        projectTaskImageDataService.delTaskId(taskId);
        taskErrorResultService.delTaskId(taskId);
    }



}
