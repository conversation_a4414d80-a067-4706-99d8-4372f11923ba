import { PageData, Result } from '../api/types'
import { UserParam, userType } from '../typings/user'
import request from './request'

export type LoginParam = {
  userCode: string
  password: string
}

export type IntBool = 0 | 1

export type OptionalValue<T> = T | null

export type UserBaseVO = {
  id: string
  code: string
  name: string
  usable: IntBool
  avatar: OptionalValue<string>
  phoneNumber: OptionalValue<string>
}

export function login(formData: LoginParam) {
  return request.post<Result<UserBaseVO>>('/user/login', formData)
}

export function getUserPage(params?: Record<string, any>) {
  return request.get<Result<PageData<userType>>>('/user/page', {
    params,
  })
}

export function getUserAll() {
  return request.get<Result<userType[]>>('/user/getAll')
}

export function saveUser(params: UserParam) {
  return request.post<Result<string>>('/user/save', params)
}

export function getCurrentUser() {
  return request.get<Result<userType>>('/user/cur')
}

export function logout() {
  return request.get<Result<userType>>('/user/logout')
}

export function modifyPwd(params: Record<string, string>) {
  return request.post<Result<string>>('/user/pwd/modify', params)
}

export function getVersion() {
  return request.get<Result<string>>('/version')
}
