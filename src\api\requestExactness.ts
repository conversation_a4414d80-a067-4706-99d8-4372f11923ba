import axios from 'axios'
import { message } from 'antd'
import { Result } from './types'
import { API_CODE } from './constant'

const request = axios.create({
  baseURL: '/api/exactness',
  headers: {
    'content-type': 'application/json',
    'x-requested-with': 'XMLHttpRequest',
  },
  withCredentials: import.meta.env.DEV,
})

request.interceptors.response.use(value => {
  const { code } = value.data as Result<any>
  if (code === API_CODE.OK) {
    return value
  } else {
    message.error(value.data.message)
  }
  return Promise.reject(value)
})

export default request
