module.exports = {
  mode: 'jit',
  purge: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  content: [],
  // important: true,
  theme: {
    extend: {
      colors: {
        primary: {
          default: '#FF770d', //#1890FF  #FF770d
          sub: 'rgba(255, 119, 13, 0.1)',
          error: '#F5222D',
          light: '#FFF1E6',
          'black-65': 'rgba(0, 0, 0, 0.65)',
          'black-85': 'rgba(0, 0, 0, 0.85)',
        },
      },

      boxShadow: {
        process: '0px 4px 8px 0px rgba(0, 0, 0, 0.08)',
        nav: '0px 1px 4px 0px rgba(0, 21, 41, 0.12)',
      },
    },
  },
  plugins: [],
}
