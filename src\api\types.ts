import { API_CODE } from './constant'

export type SuccessResult<T> = {
  code: typeof API_CODE.OK
  message: 'success'
  data: T
}

export type ErrorResult<T> = {
  code: Exclude<(typeof API_CODE)[keyof typeof API_CODE], typeof API_CODE.OK>
  message: string
  data: T
}

export type Result<T> = SuccessResult<T> | ErrorResult<T>

export interface PageInfo {
  /** 页号 */
  pageNo: number
  /** 每页数据数量 */
  pageSize: number
  /** 排序 */
  sort: string
  /** 总数据数量 */
  total: number
  /** 总页数 */
  totalPages: number
}

export interface PageData<T> extends PageInfo {
  /** 查询结果项 */
  list: T[]
}

export type IntBool = 0 | 1

export type OptionalValue<T> = T | null

export type OptionalParams<T extends object, Keys extends keyof T> = Omit<
  T,
  Keys
> & {
  [Key in Keys]?: T[Key]
}

export interface BaseDO {
  id: string
}

export interface BaseUuidDO {
  id: string
  createTime: string
}

export interface PageQueryParams {
  pageNo: number
  pageSize: number
  sortInfo?: string
  [name: string]: any
}

