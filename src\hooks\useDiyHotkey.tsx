import {
  DEFAULT_HOTKEY_MAP,
  FIXED_HOTKEY,
  HotkeyType,
} from '../constants/hotkey'

export function getHotKeyByType(
  map: Record<string, { keyList: string[]; preventDefault: boolean }>,
  type: HotkeyType
) {
  const disabled = FIXED_HOTKEY.includes(type)
  const data = disabled ? DEFAULT_HOTKEY_MAP[type] : map[type]

  function filter(e: KeyboardEvent) {
    e.preventDefault()
    return true
  }

  return {
    key: data.keyList.join('+'),
    keyList: data.keyList,
    filter: data.preventDefault ? filter : undefined,
  }
}

function useDiyHotkey(): Record<
  string,
  { keyList: string[]; preventDefault: boolean }
> {
  // const { user } = useUserContext()

  // return user?.shortcut
  //   ? {
  //       ...DEFAULT_HOTKEY_MAP,
  //       ...JSON.parse(user.shortcut),
  //     }
  //   : DEFAULT_HOTKEY_MAP
  return DEFAULT_HOTKEY_MAP
}

export function useDiyHotkeyWithTypes(types?: HotkeyType[]): Record<
  string,
  {
    key: string
    keyList: string[]
    filter: ((e: KeyboardEvent) => boolean) | undefined
  }
> {
  const hotkeyMap = useDiyHotkey()

  if (!types) {
    return (Object.keys(hotkeyMap) as HotkeyType[]).reduce((map, key) => {
      return {
        ...map,
        [key]: getHotKeyByType(hotkeyMap, key),
      }
    }, {})
  }

  return types.reduce((map, key) => {
    return {
      ...map,
      [key]: getHotKeyByType(hotkeyMap, key),
    }
  }, {})
}

export default useDiyHotkey
