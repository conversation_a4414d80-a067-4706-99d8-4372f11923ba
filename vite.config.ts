import * as path from 'path'
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import * as dotenv from 'dotenv'

// import axios from 'axios'

dotenv.config({ path: path.resolve(__dirname, '.env.local') })

const { DEV_PROXY = '' } = process.env

// 定义请求后端接口的异步函数
// async function fetchConfig() {
//   console.log(DEV_PROXY + '/api/version')
//   try {
//     const response = await axios.get(DEV_PROXY + '/api/version')
//     console.log(response.data)

//     return response.data // 假设后端返回的是需要的配置项
//   } catch (error) {
//     console.error('Error fetching config:', error)
//     return {} // 返回一个默认配置项或空对象
//   }
// }

// https://vitejs.dev/config/
export default defineConfig(async ({ mode }) => {
  // if (mode === 'production') {
  //   return {
  //     base: process.env.BASE_URL,
  //     plugins: [],
  //   }
  // }

  // const backendConfig = await fetchConfig()
  // console.log(backendConfig)
  return {
    plugins: [react()],
    server: {
      port: 2022,
      open: true,
      proxy: {
        '/api': DEV_PROXY,
      },
    },
    build: {
      sourcemap: false,
    },
    base: process.env.BASE_URL,
    css: {
      preprocessorOptions: {
        less: {
          modifyVars: {
            // 'primary-color': '#FF770d',
          },
          javascriptEnabled: true,
        },
      },
    },
  }
})
