import { max } from '.'

const DEFAULT_POWER = 2

// 不四舍五入的toFixed
function toFixed(value: number, power?: number) {
  if (!power || power === 0) {
    return String(value)
  }
  const [first, end = ''] = String(value).split('.')
  return `${first}.${end.substr(0, power).padEnd(power, '0')}`
}

const tenPower = (power: number) => (value: number) =>
  parseInt(toFixed(value, power).replace('.', ''), 10)

// 获取数字小数点后位数
function getPower(value: number) {
  const [, end] = String(value).split('.')
  return end ? end.length : 0
}

function getToInt(
  prevValue: number,
  currentValue: number
): [(value: number) => number, number] {
  const maxPower = max(getPower(prevValue), getPower(currentValue))
  const toInt = tenPower(maxPower)
  return [toInt, maxPower]
}

const math = {
  value: 0,
  mathPower: 0,
  init(value: number, mathPower: number = DEFAULT_POWER) {
    this.value = value
    this.mathPower = mathPower
    return this
  },
  // 加
  add(value: number) {
    const [toInt, power] = getToInt(this.value, value)
    const nextValue = (toInt(this.value) + toInt(value)) / 10 ** power
    this.value = Number(toFixed(nextValue, this.mathPower))
    return this
  },
  // 减
  subtract(value: number) {
    const [toInt, power] = getToInt(this.value, value)
    const nextValue = (toInt(this.value) - toInt(value)) / 10 ** power
    this.value = Number(toFixed(nextValue, this.mathPower))
    return this
  },
  // 乘
  multiply(value: number) {
    const [toInt, power] = getToInt(this.value, value)
    const nextValue = (toInt(this.value) * toInt(value)) / 10 ** (power * 2)
    this.value = Number(toFixed(nextValue, this.mathPower))
    return this
  },
  // 除
  divide(value: number) {
    const [toInt] = getToInt(this.value, value)
    const nextValue = toInt(this.value) / toInt(value)
    this.value = Number(toFixed(nextValue, this.mathPower))
    return this
  },
  end() {
    const v = this.value
    this.value = 0
    this.mathPower = 0
    return v
  },
}

// @ts-ignore
window.math = math

export default math

// testMath()

export function testMath() {
  const v1 = math.init(0.1).add(0.2).end()
  console.log(v1, v1 === 0.3, '0.1 + 0.2')
  const v2 = math.init(0.7).add(0.1).end()
  console.log(v2, v2 === 0.8, '0.7 + 0.1')
  const v3 = math.init(0.2).add(0.4).end()
  console.log(v3, v3 === 0.6, '0.2 + 0.4')

  const v4 = math.init(1.5).subtract(1.2).end()
  console.log(v4, v4 === 0.3, '1.5 - 1.2')
  const v5 = math.init(0.3).subtract(0.2).end()
  console.log(v5, v5 === 0.1, '0.3 - 0.2')

  const v6 = math.init(19.9).multiply(100).end()
  console.log(v6, v6 === 1990, '19.9 * 100')
  const v7 = math.init(0.8).multiply(0.3).end()
  console.log(v7, v7 === 0.24, '0.8 * 0.3')
  const v8 = math.init(35.41).multiply(100).end()
  console.log(v8, v8 === 3541, '35.41 * 100')

  const v9 = math.init(0.3).divide(0.1).end()
  console.log(v9, v9 === 3, '0.3 / 0.1')
  const v10 = math.init(0.69).divide(10).end()
  console.log(v10, v10 === 0.069, '0.69 / 10')
}

// // 加法 =====================
// 0.1 + 0.2 = 0.30000000000000004
// 0.7 + 0.1 = 0.7999999999999999
// 0.2 + 0.4 = 0.6000000000000001

// // 减法 =====================
// 1.5 - 1.2 = 0.30000000000000004
// 0.3 - 0.2 = 0.09999999999999998

// // 乘法 =====================
// 19.9 * 100 = 1989.9999999999998
// 0.8 * 3 = 2.4000000000000004
// 35.41 * 100 = 3540.9999999999995

// // 除法 =====================
// 0.3 / 0.1 = 2.9999999999999996
// 0.69 / 10 = 0.06899999999999999
