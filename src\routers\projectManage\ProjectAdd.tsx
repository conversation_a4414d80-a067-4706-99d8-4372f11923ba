import { close } from 'fs'
import {
  CloudUploadOutlined,
  DownOutlined,
  FileTextOutlined,
  FolderOutlined,
  LeftOutlined,
  MinusSquareOutlined,
  PlusSquareOutlined,
} from '@ant-design/icons'
import React, { useEffect, useState } from 'react'
import {
  Button,
  Form,
  Menu,
  MenuProps,
  Pagination,
  Popover,
  Radio,
  Select,
  message,
} from 'antd'
import Table, { ColumnsType } from 'antd/lib/table'
import { useNavigate, useLocation } from 'react-router-dom'
import classNames from 'classnames'
import { useMutation, useQuery } from 'react-query'
import MateSetting from '../../components/project/MateSetting'
import { getSearch } from '../../utils'
import { extractResponse } from '../../api/util'
import { getRuleByParentId } from '../../api/fieldLibrary'
import MateCheck from '../../components/project/MateCheck'
import UploadFiles from '../../components/project/UploadFiles'
import {
  mateExcel,
  mateImage,
  projectConfigDelById,
  projectConfigSave,
  statisticsImage,
  statisticsLogicRule,
  statisticsRule,
  taskConfigGetByTaskId,
  taskInfoGetById,
} from '../../api/project'
import ImageInfo from '../../components/project/ImageInfo'
import FileOutlinedIcon from '../../components/icons/FileOutlined'
import { useVersionContext } from '../../hooks/useVersion'

const defaultCatalogueList: CatalogueFile = {
  sheetName: '',
  ruleTemplateId: undefined,
  filePath: '',
  urlRule: '',
  ruleConfigType: NaN,
  taskId: '',
  libraryId: '',
  sheetNames: '',
  pieceHaveIs: 0,
  fileName: '',
}

const checkTypeList = [
  {
    label: '案卷条目',
    value: 0,
  },
  {
    label: '卷内条目',
    value: 1,
  },
  {
    label: '图像',
    value: 2,
  },
  {
    label: 'PDF',
    value: 3,
  },
  {
    label: 'OFD',
    value: 4,
  },
]

const uploadType = {
  0: 'excel',
  1: 'excel',
  2: 'image',
  3: 'pdf',
  4: 'ofd',
}

const mateMap: Record<mate, string> = {
  unImage: '未匹配的图像文件夹档号',
  unExcel: '未匹配的条目档号',
  mate: '已匹配',
}

type mate = 'unImage' | 'unExcel' | 'mate'

export default function ProjectAdd() {
  const form = Form.useForm()[0]

  const [activeConfig, setActiveConfig] = useState<CatalogueFile>()
  const [isMateSetting, setIsMateSetting] = useState(false)
  const navigate = useNavigate()
  const location = useLocation()
  const [disabled, setDisabled] = useState(false)
  const [showProject, setShowProject] = useState(true)
  const queryString = location.search
  const projectId = getSearch(queryString, 'id')
  const libraryId = getSearch(queryString, 'libraryId')
  const [showMateCheck, setShowMateCheck] = useState(false)
  const [showUpload, setShowUpload] = useState(false)
  const [activeUploadType, setActiveUploadType] = useState('')
  const [selectedKeys, setSelectedKeys] = useState<string[]>(['unImage'])
  const [activeImageInfo, setActiveImageInfo] = useState<ImageRuleInfoType[]>()
  const [activeConfigData, setActiveConfigData] = useState<string>('')
  const [param, setParam] = useState<Record<string, any>>({
    pageNo: 1,
    pageSize: 30,
    search_EQ_taskId: projectId,
    search_EQ_mateIs: 0,
  })
  const [mateData, setMateData] = useState<Record<string, string>[]>([])
  const { version } = useVersionContext()

  function xiChengText() {
    return version === 'xicheng' ? '!text-[22px]' : ''
  }

  const { data: taskInfo } = useQuery(
    ['getLibraryById', projectId],
    extractResponse(() => taskInfoGetById(projectId))
  )
  useEffect(() => {
    if (taskInfo) {
      !taskInfo.taskStatus || taskInfo.taskStatus === (0 as TProcessStatus)
        ? setDisabled(false)
        : setDisabled(true)
    }
  }, [taskInfo])

  const { data: ruleList } = useQuery(
    ['getLibraryById', libraryId],
    extractResponse(() => getRuleByParentId(libraryId)),
    {
      enabled: !!libraryId,
    }
  )

  const { data: catalogueList = [], refetch } = useQuery(
    ['taskConfigGetByTaskId', projectId],
    extractResponse(() => taskConfigGetByTaskId(projectId)),
    {
      onSuccess(res) {
        const value = res.find(
          item => item.ruleConfigType === 1 || item.ruleConfigType === 0
        )
        if (value) {
          setActiveConfigData(value.id!)
        }
      },
    }
  )

  // mateImage
  const { data, refetch: mateRefetch } = useQuery(
    ['mateImage'],
    extractResponse(() =>
      selectedKeys[0] !== ('unExcel' as mate)
        ? mateImage(param.pageNo, param.pageSize, {
            ...param,
          })
        : mateExcel(param.pageNo, param.pageSize, {
            ...param,
          })
    ),
    {
      onSuccess(res) {
        console.log(res)
        setMateData(res.list)
      },
    }
  )

  useEffect(() => {
    mateRefetch()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [param])

  const { data: imageDataList } = useQuery(
    ['statisticsImage', projectId],
    extractResponse(() => statisticsImage(projectId)),
    {
      enabled: !!projectId,
    }
  )

  const { data: ruleDataList } = useQuery(
    ['statisticsRule', projectId, activeConfigData],
    extractResponse(() => statisticsRule(projectId, activeConfigData)),
    {
      enabled: !!projectId && !!activeConfigData,
    }
  )

  const { data: logicRuleDataList } = useQuery(
    ['statisticsLogicRule', projectId, activeConfigData],
    extractResponse(() => statisticsLogicRule(projectId, activeConfigData)),
    {
      enabled: !!projectId && !!activeConfigData,
    }
  )

  function matePreview(mateList: string) {
    if (!mateList) {
      return ''
    }
    const list: Record<string, string>[] = JSON.parse(mateList)
    let text = ''
    list.forEach(item => {
      text += item.label
    })
    return text
  }

  function saveConfig(
    item: CatalogueFile,
    label: string,
    value: number | string
  ) {
    projectConfigSaveMutation.mutate({ ...item, [label]: value })
  }

  const columns: ColumnsType<CatalogueFile> = [
    {
      title: '检查类型',
      className: xiChengText(),
      render: (item: CatalogueFile) => (
        <Select
          className="w-[112px]"
          options={checkTypeList}
          disabled={disabled}
          placeholder="请选择类型"
          value={item.ruleConfigType}
          onChange={e => {
            saveConfig(
              { ...defaultCatalogueList, id: item.id, taskId: item.taskId },
              'ruleConfigType',
              e
            )
          }}
        />
      ),
    },
    {
      title: '档号规则',
      className: xiChengText(),
      render: (item: CatalogueFile) => (
        <div
          className={classNames(
            ' w-[164px] border h-8 cursor-pointer flex items-center px-2 truncate ',
            disabled ? 'bg-[#d9d9d9] bg-opacity-20' : null,
            matePreview(item.urlRule) ? null : 'text-gray-400'
          )}
          onClick={() => {
            if (!disabled) {
              setIsMateSetting(true)
              setActiveConfig(item)
            }
          }}
        >
          {matePreview(item.urlRule) || `请设置档号规则`}
        </div>
      ),
      key: 'urlRule',
    },
    {
      title: '规则模板',
      className: xiChengText(),
      render: (item: CatalogueFile) => {
        if (item.ruleConfigType === undefined || item.ruleConfigType === null) {
          return
        }
        let type = 1
        if (item?.ruleConfigType === 1 || item?.ruleConfigType === 0) {
          type = 1
        } else {
          type = 2
        }

        return (
          <div>
            <Select
              value={item.ruleTemplateId}
              className={classNames('w-[180px]', xiChengText())}
              disabled={disabled}
              placeholder="请选择规则模板"
              options={ruleList?.filter(rule => rule.librarytype === type)}
              fieldNames={{ label: 'libraryName', value: 'id' }}
              onChange={e => {
                saveConfig(item, 'ruleTemplateId', e)
              }}
            />
          </div>
        )
      },
      key: 'rule',
    },
    {
      title: '文件上传',
      className: xiChengText(),
      render: (item: CatalogueFile) => (
        <div className="flex items-center">
          <div className=" h-8 truncate"> {item.ruleConfigType>1?item.filePath:item.fileName}</div>
          {!disabled ? (
            <CloudUploadOutlined
              className="text-[#0092fe] ml-2 w-4 h-4"
              onClick={() => {
                if (!item.urlRule) {
                  message.warning('请输入档号规则')
                  return
                }
                setShowUpload(true)
                setActiveConfig(item)
                setActiveUploadType(
                  uploadType[item.ruleConfigType as 0 | 1 | 2 | 3 | 4]
                )
              }}
            />
          ) : null}
        </div>
      ),
      key: 'catalogPath',
    },

    {
      title: 'sheet表',
      className: xiChengText(),

      render: (item: CatalogueFile) => (
        <div className="flex">
          <Select
            value={item.sheetName}
            disabled={
              item.ruleConfigType === 2 ||
              item.ruleConfigType === 3 ||
              item.ruleConfigType === 4 ||
              disabled
            }
            className="w-32"
            options={item.sheetNames ? JSON.parse(item.sheetNames) : []}
            onChange={e => {
              saveConfig(item, 'sheetName', e)
            }}
          />
          {item.ruleConfigType !== 2 &&
          item.ruleConfigType !== 3 &&
          item.ruleConfigType !== 4 ? (
            <Button
              // type="link"
              onClick={() => {
                setShowMateCheck(true)
                setActiveConfig(item)
              }}
              className={classNames(
                item.ruleMapping ? `!text-[#52C41A]` : '!text-[#FF4D4F]',
                'ml-2'
              )}
            >
              {item.ruleMapping ? '已匹配' : '未匹配'}
            </Button>
          ) : null}
        </div>
      ),
      key: 'catalogPath',
    },
    {
      title: !disabled ? '操作' : null,
      key: 'operate',

      render: (item: CatalogueFile, record: CatalogueFile, index) =>
        !disabled ? (
          <div className="w-20">
            {index === 0 && catalogueList.length === 1 ? null : (
              <MinusSquareOutlined
                className="w-4 h-4 cursor-pointer"
                onClick={() => {
                  projectConfigDelByIdMutation.mutate(item.id!)
                }}
              />
            )}

            {index === catalogueList.length - 1 ? (
              <PlusSquareOutlined
                className="w-4 h-4 ml-3 cursor-pointer"
                onClick={() => {
                  projectConfigSaveMutation.mutate({
                    ...defaultCatalogueList,
                    taskId: projectId,
                  })
                }}
              />
            ) : null}
          </div>
        ) : null,
    },
  ]

  const imageStatisticsColumn: ColumnsType<ImageStatisticType> = [
    {
      title: '规则名称',
      render: (item: ImageStatisticType) => (
        <div className=" cursor-pointer">{item.ruleType}</div>
      ),
      key: 'ruleType',
      className: xiChengText(),
    },
    {
      title: '规则类型',
      render: (item: ImageStatisticType) => (
        <div className=" cursor-pointer">{item.ruleName}</div>
      ),
      key: 'ruleName',
      className: xiChengText(),
    },
    {
      title: '数量',
      render: (item: ImageStatisticType) => (
        <div className=" cursor-pointer">{item.errorNum}</div>
      ),
      key: 'errorNum',
      className: xiChengText(),
    },
  ]

  const ruleStatisticsColumn: ColumnsType<ImageStatisticType> = [
    {
      title: '字段名称',
      render: (item: ImageStatisticType) => <div>{item.fileName}</div>,
      key: 'ruleType',
      className: xiChengText(),
    },
    // {
    //   title: '字段名称',
    //   render: (item: ImageStatisticType) => {
    //     item.taskStatisticsFieldVOs?.map(field => (
    //       <div className=" cursor-pointer">{field.fieldName}</div>
    //     ))
    //   },
    //   key: 'ruleName',
    // },
    {
      title: '错误数量',
      render: (item: ImageStatisticType) => <div>{item.errorNum}</div>,
      key: 'errorNum',
      className: xiChengText(),
    },
    {
      title: '错误率',
      render: (item: ImageStatisticType) => <div>{item.errorRate || 0}%</div>,
      key: 'number',
      className: xiChengText(),
    },
  ]

  const logicStatisticsColumn: ColumnsType<ImageStatisticType> = [
    {
      title: '项',
      render: (item: ImageStatisticType) => <div>{item.ruleName}</div>,
      key: 'ruleName',
      className: xiChengText(),
    },
    {
      title: '错误数量',
      render: (item: ImageStatisticType) => <div>{item.errorNum}</div>,
      key: 'errorType',
      className: xiChengText(),
    },
    {
      title: '错误率',
      render: (item: ImageStatisticType) => <div>{item.errorRate || 0}%</div>,
      key: 'number',
      className: xiChengText(),
    },
  ]

  const projectConfigSaveMutation = useMutation(projectConfigSave, {
    onSuccess(res) {
      refetch()
    },
  })

  const projectConfigDelByIdMutation = useMutation(projectConfigDelById, {
    onSuccess(res) {
      message.open({ type: 'success', content: res.data.message })
      refetch()
    },
  })

  const items: MenuProps['items'] = [
    {
      label: '未匹配图像',
      key: 'unImage',
    },
    {
      label: '未匹配条目',
      key: 'unExcel',
    },
    {
      label: '已匹配',
      key: 'mate',
    },
  ]

  return (
    <div>
      <div className="h-full">
        <div className="flex items-center text-base h-16 pl-4 cursor-pointer">
          <div
            className="flex items-center"
            onClick={() => {
              navigate(-1)
            }}
          >
            <LeftOutlined className="w-4 h-4 mr-2" />
            <div className={xiChengText()}>任务详情</div>
          </div>

          <DownOutlined
            className={classNames(
              'ml-auto mr-6',
              showProject ? null : 'rotate-180'
            )}
            onClick={() => {
              setShowProject(!showProject)
            }}
          />
        </div>
        {showProject ? (
          <Form form={form} className="!px-6 mt-4">
            <div className={classNames('text-base', xiChengText())}>
              目录文件
            </div>
            <Table
              dataSource={catalogueList}
              columns={columns}
              pagination={false}
              className="mt-2"
              rowKey={record => record.id!}
            />
          </Form>
        ) : null}

        <MateSetting
          open={isMateSetting}
          close={setIsMateSetting}
          activeConfig={activeConfig}
          projectConfigSaveMutation={projectConfigSaveMutation}
          libraryId={libraryId}
        />
      </div>

      {queryString ? (
        <div>
          <div className="h-4 bg-[#F7F7F7]" />
          <div
            className={classNames(
              'flex items-center text-base h-16 pl-4 ',
              xiChengText()
            )}
          >
            数据匹配情况
            <Button
              className="ml-auto mr-4"
              href={'/api/exactness/taskData/mateReport?taskId=' + projectId}
            >
              导出匹配报告
            </Button>
          </div>
          <div className="pl-4 pb-4">
            <Menu
              onClick={e => {
                console.log(e.keyPath)
                setSelectedKeys(e.keyPath)
                setParam(p => ({
                  ...p,
                  pageNo: 1,
                  search_EQ_mateIs: e.keyPath[0] === 'mate' ? 1 : 0,
                }))
              }}
              selectedKeys={selectedKeys}
              mode="horizontal"
              items={items}
              className={xiChengText()}
            />
            <div className={classNames('text-xs mt-[10px]', xiChengText())}>
              {mateMap[selectedKeys[0] as mate]}
            </div>
            <div className={classNames('text-xs mt-4', xiChengText())}>
              总数量：{data?.total}件
            </div>
            <div className="ml-4 mt-5 pb-7 flex flex-wrap">
              {mateData.map(datas => (
                <div
                  className={classNames(
                    'flex items-center mr-10 mb-5',
                    xiChengText()
                  )}
                  key={datas.id}
                >
                  {selectedKeys[0] === 'unExcel' ? (
                    <FileTextOutlined />
                  ) : (
                    <FileOutlinedIcon />
                  )}
                  {(
                    datas.dataKey +
                    (datas.partNumber ? '-' + datas.partNumber : '')
                  ).length > 9 ? (
                    <Popover
                      content={
                        datas.dataKey +
                        (datas.partNumber ? '-' + datas.partNumber : '')
                      }
                      placement="top"
                    >
                      <div className="w-[129px] truncate ml-2">
                        {datas.dataKey +
                          (datas.partNumber ? '-' + datas.partNumber : '')}
                      </div>
                    </Popover>
                  ) : (
                    <div className="w-[129px] truncate ml-2">
                      {datas.dataKey +
                        (datas.partNumber ? '-' + datas.partNumber : '')}
                    </div>
                  )}
                </div>
              ))}
            </div>
            <Pagination
              total={data?.total}
              current={param.pageNo}
              onChange={e => {
                setParam(p => ({ ...p, pageNo: e }))
              }}
              pageSize={30}
              showSizeChanger={false}
              className={xiChengText()}
            />
          </div>
          <div className="h-4 bg-[#F7F7F7]" />
          {!taskInfo?.taskStatus ||
          taskInfo?.taskStatus === (0 as TProcessStatus) ? null : (
            <div className="pr-6">
              <div className="flex items-center text-base h-16 pl-4 ">
                <div className={xiChengText()}>统计数据</div>
                <Button
                  className={'ml-auto mr-4'}
                  href={
                    '/api/exactness/taskError/result/exportData?taskId=' +
                    projectId
                  }
                >
                  导出详情
                </Button>
                <Button
                  href={
                    '/api/exactness/taskError/result/exportDataPdf?taskId=' +
                    projectId
                  }
                >
                  导出报告
                </Button>
              </div>
              <Radio.Group
                style={{ marginBottom: 8 }}
                onChange={e => {
                  setActiveConfigData(e.target.value)
                }}
                defaultValue={activeConfigData}
                className="min-h-6 !ml-[30%]"
              >
                {catalogueList.map(item =>
                  item.ruleConfigType === 1 || item.ruleConfigType === 0 ? (
                    <Radio.Button value={item.id} key={item.id}>
                      {item.fileName}
                    </Radio.Button>
                  ) : null
                )}
              </Radio.Group>
              <div
                className={classNames(
                  'pl-6 w-full flex justify-around',
                  xiChengText()
                )}
              >
                <div className=" w-2/6 mr-4 ">
                  <div className="bg-[#2189F4] bg-opacity-10 h-11  flex items-center justify-center">
                    图片审查统计
                  </div>
                  <Table
                    columns={imageStatisticsColumn}
                    dataSource={imageDataList}
                    pagination={false}
                    bordered
                    onRow={record => {
                      return {
                        onClick: () => {
                          setActiveImageInfo(record.lists)
                        },
                      }
                    }}
                  />
                </div>
                <div className="w-3/6 mr-4 ">
                  <div
                    className={classNames(
                      'bg-[#FF7F37] bg-opacity-10 h-11 text-sm flex items-center justify-center',
                      xiChengText()
                    )}
                  >
                    规则审查统计
                  </div>

                  <Table
                    columns={ruleStatisticsColumn}
                    bordered
                    pagination={false}
                    dataSource={ruleDataList}
                  />
                </div>
                <div className="w-2/6 ">
                  <div
                    className={classNames(
                      'bg-[#07C160] bg-opacity-10 h-11 text-sm flex items-center justify-center ',
                      xiChengText()
                    )}
                  >
                    逻辑审查统计
                  </div>
                  <Table
                    columns={logicStatisticsColumn}
                    bordered
                    pagination={false}
                    dataSource={logicRuleDataList}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      ) : null}
      <MateCheck
        close={setShowMateCheck}
        open={showMateCheck}
        activeConfig={activeConfig}
        projectConfigSaveMutation={projectConfigSaveMutation}
        disabled={disabled}
        libraryId={libraryId}
      />
      <UploadFiles
        close={setShowUpload}
        open={showUpload}
        type={activeUploadType}
        activeConfig={activeConfig}
        refetch={refetch}
        projectConfigSaveMutation={projectConfigSaveMutation}
      />
      <ImageInfo
        close={() => setActiveImageInfo(undefined)}
        activeImageInfo={activeImageInfo}
        projectId={projectId}
      />
    </div>
  )
}
