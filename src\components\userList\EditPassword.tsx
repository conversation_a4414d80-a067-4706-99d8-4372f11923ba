import { QuestionCircleOutlined } from '@ant-design/icons'
import { Form, Input, message, Modal, Tooltip } from 'antd'
import React, { useEffect } from 'react'
import { useMutation } from 'react-query'
import { modifyPwd } from '../../api/user'

type EditPasswordType = {
  isOpen: boolean
  onClose: React.Dispatch<React.SetStateAction<boolean>>
  code: string
}

const rule = [{ required: true, message: '必填' }]

export default function EditPassword({
  isOpen,
  onClose,
  code,
}: EditPasswordType) {
  const form = Form.useForm()[0]

  useEffect(() => {
    if (isOpen) {
      form.setFieldsValue({
        newPassword: '',
        confirmPassword: '',
        oldPassword: '',
      })
    }
  }, [form, isOpen])

  const modifyPwdMutation = useMutation(modifyPwd, {
    onSuccess(res) {
      message.success(res.data.data)
      onClose(false)
    },
  })

  function submit(value: Record<string, string>) {
    if (!value.newPassword.trim()) {
      message.warning('密码不能为空格')
      return
    }
    const pwd = {
      oldPwd: value.oldPassword,
      pwd: value.newPassword,
      confirmPwd: value.confirmPassword,
      code: code,
    }
    modifyPwdMutation.mutate(pwd)
  }
  return (
    <Modal
      title={
        <div>
          密码修改
          <Tooltip
            title="初始密码：666666"
            className="text-black"
            color="#ffffff"
            overlayInnerStyle={{ color: 'black' }}
            placement="right"
          >
            <QuestionCircleOutlined className="w-4 h-4 ml-2 cursor-pointer" />
          </Tooltip>
        </div>
      }
      visible={isOpen}
      onCancel={() => onClose(false)}
      onOk={() => form.submit()}
      style={{ width: '480px' }}
      okText="确定"
      cancelText="取消"
      forceRender
    >
      <Form
        layout="vertical"
        form={form}
        onFinish={value => {
          submit(value)
        }}
      >
        <Form.Item
          label="旧密码"
          name="oldPassword"
          className="text-sm font-bold"
          rules={rule}
        >
          <Input name="oldPassword" placeholder="请输入" maxLength={40} />
        </Form.Item>
        <Form.Item
          label="新密码"
          name="newPassword"
          className="text-sm font-bold"
          rules={rule}
        >
          <Input name="newPassword" placeholder="请输入" maxLength={40} />
        </Form.Item>
        <Form.Item
          label="确认密码"
          name="confirmPassword"
          className="text-sm font-bold"
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('newPassword') === value) {
                  return Promise.resolve()
                }
                return Promise.reject(new Error('应与确认密码保持一致'))
              },
            }),
            { required: true, message: '必填' },
          ]}
        >
          <Input name="confirmPassword" placeholder="请输入" maxLength={40} />
        </Form.Item>
      </Form>
    </Modal>
  )
}
