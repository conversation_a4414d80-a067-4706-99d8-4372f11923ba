import React from 'react'
import { useFormik } from 'formik'
import { Input, Form, Button } from 'antd'
import { useMutation } from 'react-query'
import { useLocation, useNavigate } from 'react-router-dom'
import { LockOutlined, UserOutlined } from '@ant-design/icons'
import { login, LoginParam } from '../../api/user'
import { extractResponse } from '../../api/util'
import Query from '../../api/Query'
import loginBgImg from '../../style/img/loginBg.png'
import logo from '../../style/img/kubaologo.png'

const INITIAL: LoginParam = { userCode: '', password: '' }

export function Login() {
  const location = useLocation()
  const navigate = useNavigate()

  const { mutate: doLogin, isLoading } = useMutation(extractResponse(login), {
    onSuccess() {
      const original =
        new URLSearchParams(location.search).get('original') || '/'
      navigate(original, { replace: true })
    },
  })

  const formik = useFormik({
    initialValues: INITIAL,
    onSubmit(values) {
      console.log(values)
      doLogin(values)
    },
  })

  return (
    <div
      style={{
        backgroundImage: `url('${loginBgImg}')`,
        backgroundSize: '100% 100%',
      }}
      className="h-screen relative"
    >
      <img
        src={logo}
        className="w-[168px] h-[62px] absolute top-20 left-24"
        alt=""
      />
      <div className=" w-[464px] h-[388px] bg-white top-0 left-0 bottom-0 right-0 absolute m-auto rounded px-12">
        <div className=" text-primary-default font-bold text-3xl mt-10 ">
          酷豹智能合规审查
        </div>
        <div className="text-black text-base font-normal mt-2 mb-6">
          欢迎登录
        </div>
        <Form
          name="basic"
          onFinish={() => formik.submitForm()}
          autoComplete="off"
          className=" w-[368px]"
        >
          <Form.Item
            name="userCode"
            rules={[{ required: true, message: '请输入账号！' }]}
          >
            <Input
              name="userCode"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              placeholder="账户"
              prefix={<UserOutlined className=" text-primary-default" />}
              maxLength={40}
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[{ required: true, message: '请输入密码！' }]}
          >
            <Input.Password
              name="password"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              placeholder="密码"
              prefix={<LockOutlined className=" text-primary-default" />}
              maxLength={40}
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              className="font-light w-[368px] mt-6"
              loading={isLoading}
            >
              登录
            </Button>
          </Form.Item>
        </Form>
      </div>
    </div>
  )
}

export default function Sign() {
  return (
    <Query>
      <Login />
    </Query>
  )
}
