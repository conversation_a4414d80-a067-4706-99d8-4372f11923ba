import React, { useEffect } from 'react'
import { Form, Input, Modal, Radio, message } from 'antd'
import {
  QueryObserverResult,
  RefetchOptions,
  RefetchQueryFilters,
  useMutation,
} from 'react-query'
import { userType } from '../../typings/user'
import { saveUser } from '../../api/user'
import { PageData } from '../../api/types'

type CreateUserType = {
  data?: userType
  isOpen: boolean
  onClose: () => void
  refetch: <TPageData>(
    options?: (RefetchOptions & RefetchQueryFilters<TPageData>) | undefined
  ) => Promise<QueryObserverResult<PageData<userType>, unknown>>
}

const initData: userType = {
  id: '',
  name: '',
  code: '',
  aidosRole: 'admin',
  usable: 1,
}

const reg = /^[0-9a-zA-Z_]{1,}$/
export default function CreateUser({
  data,
  isOpen,
  onClose,
  refetch,
}: CreateUserType) {
  const form = Form.useForm()[0]
  useEffect(() => {
    if (isOpen) {
      if (data) {
        form.setFieldsValue(data)
      } else {
        form.setFieldsValue(initData)
      }
    }
  }, [data, form, isOpen])

  const saveMutation = useMutation(saveUser, {
    onSuccess(res) {
      message.success(res.data.data)
      refetch()
    },
  })

  function submit(value: any) {
    console.log(value)
    if (data) {
      saveMutation.mutate({ ...value, id: data.id })
    } else {
      saveMutation.mutate(value)
    }
    onClose()
  }
  return (
    <Modal
      title={data ? '编辑用户' : '创建用户'}
      visible={isOpen}
      onCancel={() => onClose()}
      onOk={() => form.submit()}
      style={{ width: '480px' }}
      okText={data ? '确定' : '创建'}
      cancelText="取消"
      forceRender
    >
      <Form
        layout="vertical"
        onFinish={value => submit(value)}
        form={form}
        initialValues={initData}
      >
        <Form.Item
          name="name"
          label="用户名"
          className="text-sm "
          rules={[
            { whitespace: true, message: '请填写用户名' },
            { required: true, message: '请填写用户名' },
          ]}
        >
          <Input type="text" maxLength={40} />
        </Form.Item>
        <Form.Item
          name="code"
          label="用户编号"
          className="text-sm "
          rules={[
            { pattern: reg, message: '只能输入数字和字母' },
            { required: true, message: '请填写用户编号' },
          ]}
        >
          <Input maxLength={40} />
        </Form.Item>
        <Form.Item name="aidosRole" label="用户类型" className="text-sm ">
          <Radio.Group>
            <Radio value={'admin'}>管理员</Radio>
            <Radio value={'user'} style={{ marginLeft: '22px' }}>
              操作员
            </Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item name="usable" label="用户状态" className="text-sm ">
          <Radio.Group disabled={data?.aidosRole === 'superAdmin'}>
            <Radio value={1}>启用</Radio>
            <Radio value={0} style={{ marginLeft: '22px' }}>
              禁用
            </Radio>
          </Radio.Group>
        </Form.Item>
      </Form>
    </Modal>
  )
}
