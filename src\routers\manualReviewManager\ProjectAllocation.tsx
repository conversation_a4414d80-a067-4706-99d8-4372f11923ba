import { Button, Input, Modal, Select, Table, message } from 'antd'
import { ColumnsType } from 'antd/lib/table'
import React, { useEffect, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { useMutation, useQuery } from 'react-query'

import { LeftOutlined } from '@ant-design/icons'
import FormItem from 'antd/es/form/FormItem'
import UserSelect from '../../components/UserSelect'
import {
  UserCheckGroupType,
  allocation,
  getUserCheckPage,
  userCheckGroupGetByTaskId,
  userCheckSave,
  userCheckSaves,
} from '../../api/project'
import { extractResponse } from '../../api/util'

import { getSearch } from '../../utils'
import { useUserContext } from '../../hooks/useUser'

const allocationStateList = ['未分配', '已分配']

export default function ProjectAllocation() {
  const navigate = useNavigate()
  const location = useLocation()
  const { user } = useUserContext()
  console.log({ user })
  const queryString = location.search
  const projectId = getSearch(queryString, 'id')
  const [param, setParam] = useState({
    pageNo: 1,
    pageSize: 10,
    search_EQ_taskId: projectId,
    search_CONTAINS_dataKey: '',
    search_EQ_checkUser: undefined,
    search_EQ_errorType: undefined,
    search_EQ_allocationState: undefined,
  })
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [selectTaskList, setSelectTaskList] = useState<UserCheckType[]>([])
  const [isOpen, setIsOpen] = useState(false)
  const [checkUser, setCheckUser] = useState<{
    userId: string
    userName: string
  }>({ userId: '', userName: '' })
  const [checkType, setCheckType] = useState(undefined)

  // const { data: userList } = useQuery(['account-page'], () =>
  //   extractResponse(() =>
  //     getUserPage({
  //       pageNo: 1,
  //       pageSize: ********,
  //     })
  //   )()
  // )

  const { data: userCheckList } = useQuery(
    ['userCheckGroupGetByTaskId', projectId],
    () => extractResponse(() => userCheckGroupGetByTaskId(projectId!))(),
    {
      enabled: !!projectId,
      onSuccess(res) {
        // const list = res.map(item => item.userCheckId)
        // setTargetKeys(list)
      },
    }
  )

  const userCheckSaveMutation = useMutation(userCheckSave, {
    onSuccess(res) {
      refetch()
    },
  })

  const userCheckSavesMutation = useMutation(userCheckSaves, {
    onSuccess(res) {
      refetch()
      setIsOpen(false)
      setSelectedRowKeys([])
    },
  })

  const allocationDataMutation = useMutation(allocation, {
    onSuccess(res) {
      console.log(res)
    },
  })

  useQuery(
    ['userCheckGroupGetByTaskId', projectId],
    () => extractResponse(() => userCheckGroupGetByTaskId(projectId))(),
    {
      enabled: !!projectId,
      onSuccess(res) {
        console.log(res)
      },
    }
  )

  const columns: ColumnsType<UserCheckType> = [
    {
      title: '序号',
      render: (_: any, item: any, index: number) => <div>{index + 1}</div>,
      key: 'index',
    },

    {
      title: '案卷级档号',
      render: (item: UserCheckType) => item.dataKey,
      key: 'manProgress',
    },
    {
      title: '分配状态',
      render: (item: UserCheckType) => (
        <div>{allocationStateList[item.allocationState || 0]} </div>
      ),
    },
    {
      title: '质检状态',
      render: (item: UserCheckType) => {
        let str = ''
        switch (item.isPass) {
          case 1:
            str = '合格'
            break
          case 0:
            str = item.errorType!+"不合格"
            break
          default:
            str = '待质检'
            break
        }
        return <div>{str}</div>
      },
    },
    {
      title: '处理人',
      render: (item: UserCheckType) => {
        const activeUser = userCheckList?.find(
          user => item.checkUser === user.userCheckId
        )
        let list: UserCheckGroupType[] = []
        if (!activeUser && item.checkUser && item.checkUserName) {
          list = [
            {
              taskId: item.taskId,
              userCheckId: item.checkUser,
              userCheckName: item.checkUserName,
            },
            ...(userCheckList || []),
          ]
        } else {
          list = [...(userCheckList || [])]
        }
        return (user?.aidosRole === 'admin' ||
          user?.aidosRole === 'superAdmin') &&
          item.checkStatus !== 3 ? (
          <Select
            options={list}
            value={item.checkUser}
            fieldNames={{ label: 'userCheckName', value: 'userCheckId' }}
            className="w-[120px]"
            onChange={(e, option) => {
              userCheckSaveMutation.mutate({
                ...item,
                checkUserName: (option as UserCheckGroupType).userCheckName,
                checkUser: e,
                allocationState: 1,
              })
            }}
          />
        ) : (
          <div>{item.checkUserName}</div>
        )
      },
      key: 'createUserName',
    },

    {
      title: '操作',
      render: (item: UserCheckType) => {
        return (
          <div className="flex text-primary-default">
            {user?.aidosRole === 'user' &&
            item.checkUser &&
            item.checkUser !== user.id ? null : (
              <div
                className=" cursor-pointer"
                onClick={() => {
                  navigate(
                    `/manualReviewManager/manualReview/${item.taskId}?id=${item.id}`
                  )
                }}
              >
                查看
              </div>
            )}
          </div>
        )
      },
      key: 'operate',
    },
  ]

  const { data, refetch } = useQuery(
    ['getUserCheckPage'],
    extractResponse(() =>
      getUserCheckPage(param.pageNo, param.pageSize, {
        ...param,
      })
    ),
    {
      onSuccess(res) {
        console.log(res)
        // setTaskList(res.list)
      },
    }
  )

  useEffect(() => {
    refetch()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [param.pageNo, param.pageSize])

  const paginationObj = {
    total: data?.total,
    showTotal: (total: any, range: any) =>
      `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
    defaultCurrent: 1,
    defaultPageSize: 10,
    onChange: (page: number) => {
      setParam(p => ({ ...p, pageNo: page }))
    },
    showSizeChanger: true,
    onShowSizeChange: (current: any, size: number) => {
      setParam(p => ({ ...p, pageSize: size }))
    },
    showQuickJumper: true,
    current: data?.pageNo,
    pageSizeOptions: [10, 50, 100],
  }
  const onSelectChange = (
    newSelectedRowKeys: React.Key[],
    selectedRows: UserCheckType[]
  ) => {
    console.log('selectedRowKeys changed: ', newSelectedRowKeys)
    setSelectedRowKeys(newSelectedRowKeys)
    setSelectTaskList(selectedRows)
  }

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
    getCheckboxProps: (record: UserCheckType) => {
      console.log({ record })
      return { disabled: record.checkStatus === 3 }
    },
  }

  return (
    <div>
      <div className="flex justify-between px-4 items-center h-16 text-base">
        <div
          className="flex items-center cursor-pointer"
          onClick={() => {
            navigate(-1)
          }}
        >
          <LeftOutlined className="w-4 h-4 mr-2" />
          <div>任务分配</div>
        </div>
        <div className={"h-10 z-10"}>
          <Input
            placeholder="案卷档号"
            className="!mr-4 !w-[200px]"
            value={param.search_CONTAINS_dataKey}
            onChange={e => {
              setParam(p => ({ ...p, search_CONTAINS_dataKey: e.target.value }))
            }}
            allowClear={true}
            maxLength={40}
          />

          <UserSelect
            className="!mr-4 !w-[200px]"
            value={param.search_EQ_checkUser}
            onChange={e => {
              setParam(p => ({ ...p, search_EQ_checkUser: e }))
            }}
            placeholder="处理人"
          />
          <Select
            placeholder="分配状态"
            className="!mr-4 !w-[200px]"
            value={param.search_EQ_allocationState}
            allowClear={true}
            onChange={e => {
              setParam(p => ({
                ...p,
                search_EQ_allocationState: e,
              }))
            }}
            options={[
              {
                label: '未分配',
                value: 0,
              },
              {
                label: '已分配',
                value: 1,
              },
            ]}
          />
          <Select
            placeholder="质检状态"
            allowClear={true}
            className=" !mr-4 min-w-[200px]  max-w-[360px]"
            value={checkType}
            // mode={"multiple"}
            onChange={(e: any) => {
              setCheckType(e)
              let obj: Record<string, any> = {}
              obj = {
                      search_EQ_errorType: '',
                      search_EQ_isPass: e.join(),
                    }

              switch (e) {
                case `99`:
                  obj = {
                    search_EQ_errorType: '',
                    search_EQ_isPass: 99,
                  }
                  break
                case `1`:
                  obj = {
                    search_EQ_errorType: '',
                    search_EQ_isPass: 1,
                  }
                  break
                case undefined:
                  obj = {
                    search_EQ_errorType: '',
                    search_EQ_isPass: '',
                  }
                  break
                default:
                  obj = {
                    search_EQ_errorType: e,
                    search_EQ_isPass: 0,
                  }
                  break
              }

              setParam(p => ({ ...p, ...obj }))
            }}
            options={[
              {
                label: '待质检',
                value: '99',
              },
              {
                label: '合格',
                value: '1',
              },
              {
                label: '档案实体',
                value: '档案实体',
              },
              {
                label: '数字化副本',
                value: '数字化副本',
              },
              {
                label: '筛密',
                value: '筛密',
              },
              {
                label: 'OCR',
                value: 'OCR',
              },
              {
                label: '条目选项',
                value: '条目选项',
              },
            ]}
          />

          <Button
            className="mr-4"
            onClick={() => {
              refetch()
            }}
          >
            搜索
          </Button>
        </div>
      </div>
      <div className="ml-4 mb-4">
        <Button
          className="mr-4"
          onClick={() => {
            if (selectedRowKeys.length > 0) {
              setIsOpen(true)
            } else {
              message.warning('请选择案卷')
            }
          }}
        >
          分配案卷
        </Button>
        <Button
          type="primary"
          onClick={() => {
            allocationDataMutation.mutateAsync(projectId!).then(() => {
              navigate('/manualReviewManager/manualReview/' + projectId)
            })
          }}
        >
          开始审查
        </Button>
      </div>

      <div>
        <Table
          dataSource={data ? data.list : []}
          columns={columns}
          pagination={paginationObj}
          className="mx-4"
          rowKey={record => record.id!}
          rowSelection={rowSelection}
        />
      </div>
      <Modal
        title="分配案卷"
        open={isOpen}
        onCancel={() => {
          setIsOpen(false)
          setCheckUser({
            userId: '',
            userName: '',
          })
          setSelectedRowKeys([])
        }}
        onOk={() => {
          const list = selectTaskList.map(item => {
            return {
              ...item,
              checkUser: checkUser.userId,
              checkUserName: checkUser.userName,
              allocationState: 1,
            }
          })
          // checkUser
          userCheckSavesMutation.mutate(list)
        }}
      >
        <div className="mt-6">正在分配任务数量：{selectedRowKeys.length}</div>
        <FormItem label="选择处理人" className="!mt-6">
          <Select
            options={userCheckList}
            fieldNames={{ label: 'userCheckName', value: 'userCheckId' }}
            onChange={(e, option) => {
              if (!(option instanceof Array)) {
                setCheckUser({
                  userId: e,
                  userName: option.userCheckName,
                })
              }
            }}
          />
        </FormItem>
      </Modal>
    </div>
  )
}
