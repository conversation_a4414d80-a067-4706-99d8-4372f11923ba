import {
  PlusOutlined,
  SyncOutlined,
  MenuFoldOutlined,
  CaretDownOutlined,
  DeleteOutlined,
  FileTextOutlined,
} from '@ant-design/icons'
import { Modal, Tree, message } from 'antd'
import { AntTreeNodeProps } from 'antd/lib/tree'
import React, { Key, useEffect, useState } from 'react'
import { useMutation } from 'react-query'
import classNames from 'classnames'
import RuleModuleAdd from '../../components/ruleModule/RuleModuleAdd'
import FilesDispose from '../../components/ruleModule/FilesDispose'
import { libraryDelById, libraryTreeVo } from '../../api/fieldLibrary'
import { extractResponse } from '../../api/util'
import RuleConfig from '../../components/ruleModule/RuleConfig'
import FileOutlinedIcon from '../../components/icons/FileOutlined'
import { useVersionContext } from '../../hooks/useVersion'

export default function RuleMould() {
  const [ruleLibrary, setRuleLibrary] = useState<RuleLibrary[]>()
  const [expandedKeys, setExpandedKeys] = useState<Key[]>([])
  const [selectedKeys, setSelectedKeys] = useState<Key[]>([])
  const [activeRule, setActiveRule] = useState<RuleMould>()
  const [activeLibrary, setActiveLibrary] = useState<Key>()
  const [addRuleModuleAdd, setAddRuleModuleAdd] = useState(false)
  const [messageApi, contextHolder] = message.useMessage()
  const [openRuleDel, setOpenRuleDel] = useState(false)
  const { version } = useVersionContext()

  useEffect(() => {
    getTree()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const libraryDelByIdMutation = useMutation(libraryDelById, {
    onSuccess(res) {
      setActiveRule(undefined)
      setOpenRuleDel(false)
      messageApi.open({ type: 'success', content: res.data.message })
      // refetch()
      getTree()
    },
  })

  function xiChengText() {
    return version === 'xicheng' ? '!text-[22px]' : ''
  }

  function getTree() {
    libraryTreeVoMutation.mutate({ libraryLevel: 1, libraryName: '' })
  }

  const libraryTreeVoMutation = useMutation(extractResponse(libraryTreeVo), {
    onSuccess(res) {
      console.log(res)
      setRuleLibrary(res)
    },
  })

  return (
    <div className="px-4">
      {contextHolder}
      <div className="flex items-center  justify-between h-16">
        <div className={classNames('text-base', xiChengText())}>规则模板</div>
      </div>
      <div className="flex h-[46px] items-center justify-between">
        <div className="flex text-sm  h-[46px] ml-2 items-center">
          <div className={xiChengText()}>当前规则模板：</div>
          <div className={classNames('truncate', xiChengText())}>
            {activeRule?.title}
          </div>
        </div>
      </div>
      <div className="flex">
        <div className="flex mt-2 ">
          <div className=" w-64 h-[694px] border">
            <div className=" flex  bg-black bg-opacity-[2%] h-10 items-center pr-4">
              <PlusOutlined
                className=" mr-3 cursor-pointer ml-auto"
                onClick={() => {
                  if (activeLibrary) {
                    setAddRuleModuleAdd(true)
                  }
                }}
              />
              {selectedKeys.length > 0 && selectedKeys[0] === activeRule?.id ? (
                <DeleteOutlined
                  className=" mr-3 cursor-pointer"
                  onClick={() => {
                    if (selectedKeys[0] === activeRule?.id) {
                      setOpenRuleDel(true)
                    }
                  }}
                />
              ) : null}

              <SyncOutlined
                className=" mr-3 cursor-pointer"
                onClick={() => {
                  getTree()
                }}
              />
              <MenuFoldOutlined
                className=" cursor-pointer"
                onClick={() => {
                  setExpandedKeys([])
                  setSelectedKeys([])
                }}
              />
            </div>
            <div className="mt-4 overflow-y-auto p-3 h-[620px]">
              <Tree
                className={xiChengText()}
                selectedKeys={selectedKeys}
                expandedKeys={expandedKeys}
                onSelect={(item, e: AntTreeNodeProps) => {
                  console.log(item, e.node.parentId)
                  const id = e.node.key
                  let i: number = 0
                  expandedKeys.forEach((key, index) => {
                    if (key === id) {
                      i = index + 1
                    }
                  })
                  setSelectedKeys(item)
                  if (e.node.parentId) {
                    setActiveLibrary(e.node.parentId)
                  } else {
                    setActiveLibrary(item[0])
                  }
                  if (i) {
                    const list = [...expandedKeys]
                    list.splice(i - 1, 1)
                    if (e.node.pos.split('-').length === 3) {
                      setSelectedKeys([])
                      setActiveRule({
                        id: e.node.id,
                        title: e.node.title,
                        //@ts-ignore
                        type: e.node.type,
                        key: e.node.id,
                        libraryId: e.node.parentId,
                      })
                    } else {
                      setSelectedKeys([])
                      setExpandedKeys(list)
                    }
                  } else {
                    if (e.node.pos.split('-').length === 3) {
                      setActiveRule({
                        id: e.node.id,
                        title: e.node.title,
                        //@ts-ignore
                        type: e.node.type,
                        key: e.node.id,
                        libraryId: e.node.parentId,
                      })
                    } else {
                      console.log({ item })
                      setExpandedKeys(p => [...p, ...item])
                    }
                  }
                }}
                switcherIcon={(e: AntTreeNodeProps) => (
                  <CaretDownOutlined
                    onClick={() => {
                      const id = e.data.key
                      let i: number = 0
                      expandedKeys.forEach((key, index) => {
                        if (key === id) {
                          i = index + 1
                        }
                      })
                      if (i) {
                        const list = [...expandedKeys]
                        list.splice(i - 1, 1)
                        setExpandedKeys(list)
                      } else {
                        setExpandedKeys(p => [...p, id])
                      }
                    }}
                  />
                )}
                treeData={ruleLibrary}
                checkable={false}
                blockNode
                showIcon={true}
                icon={(item: AntTreeNodeProps) => {
                  if (item.pos.split('-').length === 3) {
                    return item.data.type === 2 ? (
                      <span className="anticon anticon-folder">
                        <FileOutlinedIcon
                          className={classNames(
                            version === 'xicheng' ? 'w-5 h-5' : 'w-4 h-4'
                          )}
                        />
                      </span>
                    ) : (
                      <FileTextOutlined
                        className={classNames(
                          version === 'xicheng' ? 'w-5 h-5' : 'w-4 h-4'
                        )}
                      />
                    )
                  } else {
                    return null
                  }
                }}
              />
            </div>
          </div>
        </div>
        <div className="ml-4 w-full">
          {activeRule?.type === 2 && <FilesDispose library={activeRule} />}
          {activeRule?.type === 1 && <RuleConfig library={activeRule} />}
        </div>
      </div>

      <RuleModuleAdd
        open={addRuleModuleAdd}
        close={setAddRuleModuleAdd}
        activeLibrary={activeLibrary}
        refetch={getTree}
      />
      <Modal
        title="删除规则"
        open={openRuleDel}
        onCancel={() => setOpenRuleDel(false)}
        onOk={() => libraryDelByIdMutation.mutate(activeRule?.id!)}
      >
        <div>是否删除该规则</div>
      </Modal>
    </div>
  )
}
