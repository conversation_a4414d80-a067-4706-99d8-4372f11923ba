import { Button, Input, Modal, Table, message } from 'antd'
import { ColumnsType } from 'antd/lib/table'
import classNames from 'classnames'
import React, { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useMutation, useQuery } from 'react-query'

import { StatusText } from '../../constants/project'
import UserSelect from '../../components/UserSelect'
import ProjectCreate from '../../components/project/ProjectCreate'
import DynamicStatusText from '../../components/DynamicStatusText'
import {
  allocation,
  getTaskInfoPage,
  taskInfoDelById,
  workContinue,
  workStart,
  workStop,
  workSuspend,
} from '../../api/project'
import { extractResponse } from '../../api/util'
import { useVersionContext } from '../../hooks/useVersion'

export default function ProjectManage() {
  const navigate = useNavigate()
  const [param, setParam] = useState({
    pageNo: 1,
    pageSize: 10,
    search_EQ_taskName: '',
    search_EQ_createUserId: undefined,
    search_EQ_createTime: '',
  })
  const [isDelOpen, setIsDelOpen] = useState(false)
  const [createTask, setCreateTask] = useState(false)
  const [activeTask, setActiveTask] = useState<ProjectType>()
  const [taskList, setTaskList] = useState<ProjectType[]>([])
  const { version } = useVersionContext()

  const colorMap = {
    0: 'bg-[#000] bg-opacity-[15%]',
    1: 'bg-[#1677FF]',
    2: 'bg-primary-default',
    3: 'bg-[#52C41A]',
  }

  const workStartMutation = useMutation(workStart, {
    onSuccess(res) {
      message.success(res.data.message)
      refetch()
    },
  })
  const workStopMutation = useMutation(workStop, {
    onSuccess(res) {
      message.success(res.data.message)
      refetch()
    },
  })

  const workContinueMutation = useMutation(workContinue, {
    onSuccess(res) {
      message.success(res.data.message)
      refetch()
    },
  })

  const workSuspendMutation = useMutation(workSuspend, {
    onSuccess(res) {
      message.success(res.data.message)
      refetch()
    },
  })

  // const { data: allocationData, refetch } = useQuery(
  //   ['allocation', projectId],
  //   projectId ? extractResponse(() => allocation(projectId)) : () => undefined,
  //   {
  //     enabled: !!projectId,
  //     onSuccess(res) {},
  //     onError(res) {
  //       navigate(-1)
  //     },
  //   }
  // )

  const allocationDataMutation = useMutation(allocation, {
    onSuccess(res) {
      console.log(res)
    },
  })

  function xiChengText() {
    return version === 'xicheng' ? '!text-[22px]' : ''
  }

  const columns: ColumnsType<ProjectType> = [
    {
      title: '任务名称',
      render: item => <div className={xiChengText()}>{item.taskName}</div>,
      key: 'taskName',
      className: xiChengText(),
    },
    {
      title: '进度',
      render: (item: ProjectType) => {
        let index = 0
        switch (true) {
          case item.taskProgress > 0 && item.taskProgress < 20:
            index = 0
            break
          case item.taskProgress >= 20 && item.taskProgress < 40:
            index = 1
            break
          case item.taskProgress >= 40 && item.taskProgress < 60:
            index = 2
            break
          case item.taskProgress >= 60 && item.taskProgress < 80:
            index = 3
            break
          case item.taskProgress >= 80 && item.taskProgress < 100:
            index = 4
            break
          case item.taskProgress === 100:
            index = 5
            break
        }
        return (
          <div className="flex items-center">
            <div className="flex items-center">
              {[1, 1, 1, 1, 1].map((_, i) => (
                <div
                  className={classNames(
                    'w-[2px] h-2 mr-0.5 ',
                    i < index
                      ? colorMap[item.taskStatus]
                      : 'bg-[#000] bg-opacity-[15%]'
                  )}
                />
              ))}
            </div>
            <div className={classNames('ml-2', xiChengText())}>
              {item.taskProgress || 0}%
            </div>
            <div className={classNames('ml-2', xiChengText())}>
              <DynamicStatusText
                status={item.taskStatus || 0}
                baseText={StatusText[item.taskStatus || 0]}
              />
            </div>
          </div>
        )
      },
      key: 'taskStatus',
      className: xiChengText(),
    },
    // {
    //   title: '人工进度',
    //   render: (item: ProjectType) => (
    //     <div className="flex items-center">
    //       {/* <div
    //         className={classNames(
    //           'w-[6px] h-[6px] rounded-full mr-2',
    //           colorMap[item.userCheckProgress || 0]
    //         )}
    //       /> */}
    //       {/* <div>{ManStatusText[item.userCheckProgress || 0]}</div> */}
    //       <div className="ml-2">{item.userCheckProgress || 0} %</div>
    //       {item.userCheckProgress === 100 ? (
    //         <Button
    //           href={'/api/exactness/userCheck/reportPdf?taskId=' + item.id}
    //           className="ml-4 !text-primary-default"
    //           type="link"
    //         >
    //           导出报告
    //         </Button>
    //       ) : null}
    //     </div>
    //   ),
    //   key: 'manProgress',
    // },
    {
      title: '创建人',
      render: (item: ProjectType) => (
        <div className={xiChengText()}>{item.createUserName}</div>
      ),
      key: 'createUserName',
      className: xiChengText(),
    },
    {
      title: '创建时间',
      render: (item: ProjectType) => (
        <div className={xiChengText()}>{item.createTime}</div>
      ),
      key: 'createTime',
      className: xiChengText(),
    },
    {
      title: '操作',
      className: xiChengText(),

      render: (item: ProjectType) => {
        return (
          <div className="flex text-primary-default">
            {item.taskStatus !== 2 ? (
              <div
                className={classNames('cursor-pointer  mr-4 ', xiChengText())}
                onClick={() => {
                  if (item.taskStatus === 1) {
                    workStopMutation.mutate(item.id!)
                    return
                  } else {
                    workStartMutation.mutate(item.id!)
                    return
                  }
                }}
              >
                {item.taskStatus === 1 ? '终止' : '启动'}
              </div>
            ) : null}

            {item.taskStatus === 1 ||
            item.taskStatus === (2 as TProcessStatus) ? (
              <div
                className={classNames('cursor-pointer mr-4 ', xiChengText())}
                onClick={() => {
                  if (item.taskStatus === 2) {
                    workContinueMutation.mutate(item.id!)
                    return
                  } else {
                    workSuspendMutation.mutate(item.id!)
                    return
                  }
                }}
              >
                {item.taskStatus === 2 ? '继续' : '暂停'}
              </div>
            ) : null}

            {/* <div className="ml-4">人工抽查</div> */}
            <div
              className={classNames(' cursor-pointer', xiChengText())}
              onClick={() => {
                console.log({ item })
                navigate(
                  '/projectManage/ProjectAdd?id=' +
                    item.id +
                    '&libraryId=' +
                    item.libraryId +
                    '&view=true'
                )
              }}
            >
              查看详情
            </div>
            {/* {version === 'default' && (
              <div
                className={classNames(
                  'ml-4',
                  item.taskStatus === 0 || !item.taskStatus
                    ? 'text-[#000000] text-opacity-25'
                    : 'text-primary-default cursor-pointer'
                )}
                onClick={() => {
                  if (item.taskStatus !== 0 && item.taskStatus) {
                    allocationDataMutation.mutateAsync(item.id!).then(() => {
                      navigate('/projectManage/manualReview/' + item.id)
                    })
                  }
                }}
              >
                人工审查
              </div>
            )} */}

            <div
              className={classNames(
                'ml-4 ',
                xiChengText(),
                item.taskStatus === 1
                  ? 'text-[#000000] text-opacity-25'
                  : 'text-primary-default cursor-pointer'
              )}
              onClick={() => {
                if (item.taskStatus !== 1) {
                  setActiveTask(item)
                  setIsDelOpen(true)
                }
              }}
            >
              删除
            </div>
          </div>
        )
      },
      key: 'operate',
    },
  ]

  const { data, refetch } = useQuery(
    ['getTaskInfoPage'],
    extractResponse(() =>
      getTaskInfoPage(param.pageNo, param.pageSize, {
        ...param,
      })
    ),
    {
      refetchInterval: taskList.find(item => item.taskStatus === 1)
        ? 1000
        : false,
      onSuccess(res) {
        setTaskList(res.list)
      },
    }
  )

  useEffect(() => {
    refetch()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [param.pageNo])

  const paginationObj = {
    total: data?.total,
    showTotal: (total: any, range: any) =>
      `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
    defaultCurrent: 1,
    defaultPageSize: 10,
    onChange: (page: number) => {
      setParam(p => ({ ...p, pageNo: page }))
    },
    showSizeChanger: false,
    onShowSizeChange: (current: any, size: number) => {
      setParam(p => ({ ...p, pageSize: size }))
    },
    showQuickJumper: true,
    current: data?.pageNo,
    className: xiChengText(),
  }

  const taskInfoDelByIdMutation = useMutation(taskInfoDelById, {
    onSuccess(res) {
      message.success(res.data.message)
      setIsDelOpen(false)
      if (data?.list.length === 1 && data?.pageNo !== 1) {
        setParam(p => ({ ...p, pageNo: data?.pageNo - 1 }))
      } else {
        refetch()
      }
    },
  })

  return (
    <div>
      <div className="flex justify-between px-4 items-center h-16 text-base">
        <div className={xiChengText()}>任务管理</div>
        <div className="flex ">
          <Input
            placeholder="任务名称"
            className="!mr-4 !w-[200px]"
            value={param.search_EQ_taskName}
            onChange={e => {
              setParam(p => ({ ...p, search_EQ_taskName: e.target.value }))
            }}
            maxLength={40}
          />
          {/* <Input
            placeholder="创建人"
            className="!mr-4 !w-[200px]"
            value={param.search_EQ_handlerUser}
            onChange={e => {
              setParam(p => ({ ...p, search_EQ_handlerUser: e.target.value }))
            }}
          /> */}
          <UserSelect
            className="!mr-4 !w-[200px]"
            value={param.search_EQ_createUserId}
            onChange={e => {
              setParam(p => ({ ...p, search_EQ_createUserId: e }))
            }}
          />
          {/* <Input
            placeholder="创建时间"
            className="!mr-4"
            value={param.search_EQ_createTime}
            onChange={e => {
              setParam(p => ({ ...p, search_EQ_createTime: e.target.value }))
            }}
          /> */}
          {/* <DatePicker
            className="!mr-4 !w-[200px]"
            onChange={e => {
              if (e) {
                setParam(p => ({
                  ...p,
                  search_EQ_createTime:
                    e?.year() + '-' + (e?.month() + 1) + '-' + e?.date(),
                }))
              }
            }}
          /> */}

          <Button
            className="mr-4"
            onClick={() => {
              refetch()
            }}
          >
            搜索
          </Button>
          <Button
            type="primary"
            onClick={() => {
              setCreateTask(true)
            }}
          >
            新建任务
          </Button>
        </div>
      </div>
      <div>
        <Table
          dataSource={data ? data.list : []}
          columns={columns}
          pagination={paginationObj}
          className="mx-4 "
          rowKey={record => record.id!}
        />
      </div>
      <Modal
        title="确认删除"
        open={isDelOpen}
        onOk={() => {
          taskInfoDelByIdMutation.mutate(activeTask?.id!)
        }}
        onCancel={() => setIsDelOpen(false)}
      >
        <p className=" text-primary-error">是否确认删除此任务?</p>
        <p>删除后会清空报告以及对应的文件和检测报告。</p>
      </Modal>
      <ProjectCreate
        open={createTask}
        close={() => {
          setCreateTask(false)
        }}
      />
    </div>
  )
}
