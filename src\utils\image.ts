import axios, { Canceler } from 'axios'

export function getInitialImageSize(
  width: number,
  height: number,
  box: { width: number; height: number }
) {
  const { width: w, height: h } = box
  if (width <= w && height <= h) {
    return { width, height }
  }
  const rectRatio = w / h
  const imageRatio = width / height
  if (rectRatio > imageRatio) {
    return {
      width: h * imageRatio,
      height: h,
    }
  }
  return {
    width: w,
    height: w / imageRatio,
  }
}

export function loadImage(url: string | Blob, clear?: boolean) {
  return new Promise<HTMLImageElement>(resolve => {
    const image = new Image()
    image.crossOrigin = 'anonymous'
    image.addEventListener('load', () => {
      resolve(image)
    })
    image.src =
      typeof url === 'string'
        ? clear
          ? `${url}?t=${Date.now()}`
          : url
        : URL.createObjectURL(url)
  })
}

export function fetchImage(
  url: string,
  clear?: boolean
): [Promise<HTMLImageElement>, Canceler | undefined] {
  let cancel: Canceler | undefined = undefined
  const task = axios
    .get<Blob>(clear ? `${url}?t=${Date.now()}` : url, {
      responseType: 'blob',
      cancelToken: new axios.CancelToken(c => (cancel = c)),
    })
    .then(resp => {
      return loadImage(resp.data)
    })
  return [task, cancel]
}
