import React, { AriaAttributes, ReactNode } from 'react'

export interface SvgProps {
  className?: string
  width?: number | string
  height?: number | string
  fill?: string
  stroke?: string
}

export interface BaseSvgProps extends AriaAttributes, SvgProps {
  viewBox?: string
  children?: ReactNode
}

export default function BaseSvg({ children, ...rest }: BaseSvgProps) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" {...rest}>
      {children}
    </svg>
  )
}
