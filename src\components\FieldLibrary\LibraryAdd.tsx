import { Modal, Input, Form } from 'antd'
import FormItem from 'antd/es/form/FormItem'
import React, { useEffect } from 'react'
import {
  QueryObserverResult,
  RefetchOptions,
  RefetchQueryFilters,
  useMutation,
} from 'react-query'
import { librarySave } from '../../api/fieldLibrary'
import { extractResponse } from '../../api/util'

type LibraryAddType = {
  open: boolean
  close: () => void
  refetch: <TPageData>(
    options?: (RefetchOptions & RefetchQueryFilters<TPageData>) | undefined
  ) => Promise<QueryObserverResult<RuleLibrary[], unknown>>
  data?: RuleLibrary
}

export default function LibraryAdd({
  open,
  close,
  refetch,
  data,
}: LibraryAddType) {
  const form = Form.useForm()[0]

  useEffect(() => {
    if (open) {
      if (data) {
        form.setFieldsValue({ libraryName: data.title })
      } else {
        form.setFieldsValue({ libraryName: '' })
      }
    } else {
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open])

  const librarySaveMutation = useMutation(extractResponse(librarySave), {
    onSuccess(res) {
      console.log(res)
      close()
      refetch()
    },
  })

  function submit(values: any) {
    console.log(values)
    if (data) {
      librarySaveMutation.mutate({ ...values, id: data.id })
    } else {
      librarySaveMutation.mutate(values)
    }
  }

  return (
    <Modal
      title={data ? '重命名' : '新建字段库表'}
      open={open}
      onCancel={() => {
        close()
      }}
      onOk={() => {
        form.submit()
      }}
    >
      <Form form={form} onFinish={submit}>
        <FormItem
          label="字段库名称"
          rules={[{ required: true, message: '请输入字段库表名称' }]}
          name="libraryName"
        >
          <Input
            maxLength={40}
            onChange={e => {
              form.setFieldValue('libraryName', e.target.value.trim())
            }}
          />
        </FormItem>
      </Form>
    </Modal>
  )
}
