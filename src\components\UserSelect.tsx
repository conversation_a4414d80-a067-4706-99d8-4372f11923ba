import React, { useState } from 'react'
import { useQuery } from 'react-query'
import { Select } from 'antd'
import { getUserPage } from '../api/user'
import { extractResponse } from '../api/util'

type UserSelectType = {
  onChange?: (e: any) => void
  className?: string
  value?: any
  placeholder?: string
}

export default function UserSelect({
  value,
  onChange,
  className,
  placeholder,
}: UserSelectType) {
  const [params] = useState<Record<string, any>>({
    pageNo: 1,
    pageSize: ********,
  })

  const { data } = useQuery(['account-page'], () =>
    extractResponse(() => getUserPage(params))()
  )

  return (
    <Select
      showSearch
      placeholder={placeholder || '创建人'}
      optionFilterProp="children"
      onChange={e => {
        if (onChange) {
          onChange(e)
        }
      }}
      className={className}
      filterOption={(input, option) =>
        (option?.name ?? '').toLowerCase().includes(input.toLowerCase())
      }
      value={value}
      options={data?.list}
      fieldNames={{ label: 'name', value: 'id' }}
      key={'id'}
      allowClear={true}
    />
  )
}
